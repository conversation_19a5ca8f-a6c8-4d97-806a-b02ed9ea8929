<?php
session_start();
include_once '../server/db_connect.php';


if (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'owner' && $_SESSION['role'] !== 'worker')) {
    echo "Unauthorized access";
    exit();
}


$create_payment_records = "
CREATE TABLE IF NOT EXISTS payment_records (
    payment_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    payment_period VARCHAR(7) NOT NULL COMMENT 'Format: YYYY-MM',
    payment_date DATETIME NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('PAID', 'CANCELLED', 'REFUNDED') NOT NULL DEFAULT 'PAID',
    cancelled_date DATETIME NULL,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(subject_id) ON DELETE CASCADE,
    INDEX idx_student_subject (student_id, subject_id),
    INDEX idx_payment_period (payment_period),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";


$create_payment_logs = "
CREATE TABLE IF NOT EXISTS payment_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    payment_period VARCHAR(7) NOT NULL,
    action_type ENUM('PAYMENT', 'CANCELLATION', 'REFUND') NOT NULL,
    action_date DATETIME NOT NULL,
    action_by INT NOT NULL,
    notes TEXT NULL,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(subject_id) ON DELETE CASCADE,
    FOREIGN KEY (action_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_student_subject (student_id, subject_id),
    INDEX idx_payment_period (payment_period)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Execute the queries
try {
    if ($conn->query($create_payment_records)) {
        echo "payment_records table created successfully<br>";
    } else {
        echo "Error creating payment_records table: " . $conn->error . "<br>";
    }
    
    if ($conn->query($create_payment_logs)) {
        echo "payment_logs table created successfully<br>";
    } else {
        echo "Error creating payment_logs table: " . $conn->error . "<br>";
    }
    
    echo "<p>Tables created successfully. <a href='../../frontend/worker/monthly_payments.php'>Go back to Monthly Payments</a></p>";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

$conn->close();
?>
