<?php
session_start();
include_once '../../backend/server/db_connect.php';


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];


$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_id = null;
if ($row = $result->fetch_assoc()) {
    $teacher_id = $row['teacher_id'];
}
$stmt->close();


$result = $conn->query("SELECT subject_id, subject_name FROM subjects ORDER BY subject_name");
$all_subjects = [];
while ($row = $result->fetch_assoc()) {
    $all_subjects[] = $row;
}


$stmt = $conn->prepare("
    SELECT DISTINCT s.subject_id
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_subject_ids = [];
while ($row = $result->fetch_assoc()) {
    $teacher_subject_ids[] = $row['subject_id'];
}
$stmt->close();

// Get fee information for all subjects
$subject_fees = [];
$fees_result = $conn->query("
    SELECT subject_id, grade, fee
    FROM subject_fees
    ORDER BY subject_id, grade
");
while ($fee_row = $fees_result->fetch_assoc()) {
    if (!isset($subject_fees[$fee_row['subject_id']])) {
        $subject_fees[$fee_row['subject_id']] = [];
    }
    $subject_fees[$fee_row['subject_id']][$fee_row['grade']] = $fee_row['fee'];
}


$currentPage = basename($_SERVER['PHP_SELF']);
include_once '../../assets/teacher_sidebar.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Subjects</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
        }
        .content {
            margin-left: 260px;
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        @media (max-width: 768px) {
            .content {
                margin-left: 0;
                padding-top: 60px;
            }
        }

        @media (min-width: 769px) and (max-width: 992px) {
            .content {
                margin-left: 70px;
            }
        }

        .subject-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            margin-bottom: 20px;
        }

        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }

        .subject-english .card-header {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
        }

        .subject-science .card-header {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        .subject-mathematics .card-header {
            background: linear-gradient(135deg, #f46b45 0%, #eea849 100%);
        }

        .subject-default .card-header {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
        }

        .badge-teaching {
            background-color: #28a745;
            color: white;
        }

        .badge-not-teaching {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="content">
        <div class="container-fluid">
            <div class="row mb-4">
                <div class="col-12">
                    <h1 class="mb-3"><i class="fas fa-book me-2"></i>All Subjects</h1>
                    <p class="text-muted">View all available subjects in the system</p>
                </div>
            </div>

            <div class="row">
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0"><i class="fas fa-list me-2"></i>Subject List</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Subject ID</th>
                                            <th>Subject Name</th>
                                            <th>Status</th>
                                            <th>Fee Range</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($all_subjects as $subject): ?>
                                            <?php
                                               
                                                $is_teaching = in_array($subject['subject_id'], $teacher_subject_ids);

                                                
                                                $fee_range = 'Not set';
                                                if (isset($subject_fees[$subject['subject_id']])) {
                                                    $fees = $subject_fees[$subject['subject_id']];
                                                    if (!empty($fees)) {
                                                        $min_fee = min($fees);
                                                        $max_fee = max($fees);
                                                        if ($min_fee == $max_fee) {
                                                            $fee_range = 'LKR ' . number_format($min_fee, 2);
                                                        } else {
                                                            $fee_range = 'LKR ' . number_format($min_fee, 2) . ' - ' . number_format($max_fee, 2);
                                                        }
                                                    }
                                                }
                                            ?>
                                            <tr>
                                                <td><?= $subject['subject_id'] ?></td>
                                                <td>
                                                    <strong><?= htmlspecialchars($subject['subject_name']) ?></strong>
                                                </td>
                                                <td>
                                                    <?php if ($is_teaching): ?>
                                                        <span class="badge badge-teaching">Teaching</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-not-teaching">Not Teaching</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= $fee_range ?></td>
                                                <td>
                                                    <?php if ($is_teaching): ?>
                                                        <a href="manage_fees.php" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-money-bill-wave me-1"></i> Manage Fees
                                                        </a>
                                                        <a href="subject_details.php?subject_id=<?= $subject['subject_id'] ?>" class="btn btn-sm btn-outline-info ms-1">
                                                            <i class="fas fa-info-circle me-1"></i> Details
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="add_subject.php?subject_id=<?= $subject['subject_id'] ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-plus me-1"></i> Add to My Subjects
                                                        </a>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
