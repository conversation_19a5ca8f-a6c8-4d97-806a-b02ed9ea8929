<?php
session_start();


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'worker') {
    header("Location: ../../index.php");
    exit();
}


include_once '../../backend/server/db_connect.php';


require_once '../../libs/tcpdf/tcpdf.php';


$month = isset($_GET['month']) ? intval($_GET['month']) : date('n');
$year = isset($_GET['year']) ? intval($_GET['year']) : date('Y');
$subject_filter = isset($_GET['subject']) ? $_GET['subject'] : '';
$grade_filter = isset($_GET['grade']) ? $_GET['grade'] : '';


$month_name = date('F', mktime(0, 0, 0, $month, 1, $year));

// Calculate the weeks in the month
$days_in_month = cal_days_in_month(CAL_GREGORIAN, $month, $year);
$weeks = [];
$current_week = 1;
for ($day = 1; $day <= $days_in_month; $day++) {
    $date = sprintf('%04d-%02d-%02d', $year, $month, $day);
    $week_number = ceil($day / 7);
    if (!isset($weeks[$week_number])) {
        $weeks[$week_number] = [
            'start' => $date,
            'end' => $date
        ];
    } else {
        $weeks[$week_number]['end'] = $date;
    }
}


$query = "
    SELECT DISTINCT s.student_id, CONCAT(u.first_name, ' ', u.last_name) AS student_name,
           s.grade, sub.subject_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN student_subjects ss ON s.student_id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.subject_id
    WHERE 1=1
";

if (!empty($subject_filter)) {
    $query .= " AND sub.subject_name = '$subject_filter'";
}

if (!empty($grade_filter)) {
    $query .= " AND s.grade = '$grade_filter'";
}

$query .= " ORDER BY s.grade, student_name";
$students_result = $conn->query($query);


class MYPDF extends TCPDF {
    public function Header() {
        $this->SetFont('helvetica', 'B', 15);
        $this->Cell(0, 15, 'Gurukula Institution - Monthly Attendance Report', 0, false, 'C', 0, '', 0, false, 'M', 'M');
    }

    public function Footer() {
        $this->SetY(-15);
        $this->SetFont('helvetica', 'I', 8);
        $this->Cell(0, 10, 'Page '.$this->getAliasNumPage().'/'.$this->getAliasNbPages(), 0, false, 'C', 0, '', 0, false, 'T', 'M');
    }
}


$pdf = new MYPDF('L', 'mm', 'A4', true, 'UTF-8', false);


$pdf->SetCreator('Gurukula Institution');
$pdf->SetAuthor('Gurukula Institution');
$pdf->SetTitle('Monthly Attendance Report - ' . $month_name . ' ' . $year);
$pdf->SetSubject('Attendance Report');


$pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, PDF_HEADER_TITLE, PDF_HEADER_STRING);


$pdf->SetMargins(10, 20, 10);
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(10);


$pdf->SetAutoPageBreak(TRUE, 15);


$pdf->AddPage();


$pdf->SetFont('helvetica', 'B', 12);


$title = 'Monthly Attendance Report - ' . $month_name . ' ' . $year;
if (!empty($subject_filter)) {
    $title .= ' - Subject: ' . $subject_filter;
}
if (!empty($grade_filter)) {
    $title .= ' - Grade: ' . $grade_filter;
}
$pdf->Cell(0, 10, $title, 0, 1, 'C');
$pdf->Ln(5);


$pdf->SetFont('helvetica', 'B', 10);
$pdf->SetFillColor(230, 230, 230);


$student_id_width = 25;
$name_width = 50;
$grade_width = 20;
$subject_width = 40;
$week_width = (277 - $student_id_width - $name_width - $grade_width - $subject_width) / count($weeks);


$pdf->Cell($student_id_width, 10, 'Student ID', 1, 0, 'C', 1);
$pdf->Cell($name_width, 10, 'Name', 1, 0, 'C', 1);
$pdf->Cell($grade_width, 10, 'Grade', 1, 0, 'C', 1);
$pdf->Cell($subject_width, 10, 'Subject', 1, 0, 'C', 1);

foreach ($weeks as $week_num => $week_dates) {
    $week_label = 'Week ' . $week_num . "\n" . date('d/m', strtotime($week_dates['start'])) . '-' . date('d/m', strtotime($week_dates['end']));
    $pdf->MultiCell($week_width, 10, $week_label, 1, 'C', 1, 0);
}
$pdf->Ln();


$pdf->SetFont('helvetica', '', 9);
$pdf->SetFillColor(255, 255, 255);

if ($students_result->num_rows > 0) {
    while ($student = $students_result->fetch_assoc()) {
        $pdf->Cell($student_id_width, 10, $student['student_id'], 1, 0, 'C');
        $pdf->Cell($name_width, 10, $student['student_name'], 1, 0, 'L');
        $pdf->Cell($grade_width, 10, $student['grade'], 1, 0, 'C');
        $pdf->Cell($subject_width, 10, $student['subject_name'], 1, 0, 'L');

        foreach ($weeks as $week_num => $week_dates) {
            $week_start = $week_dates['start'];
            $week_end = $week_dates['end'];

            // Check if student was present in this week
            $attendance_query = "
                SELECT COUNT(*) as present_count
                FROM attendance
                WHERE student_id = '{$student['student_id']}'
                AND date BETWEEN '$week_start' AND '$week_end'
            ";
            $attendance_result = $conn->query($attendance_query);
            $attendance_row = $attendance_result->fetch_assoc();

            $status = ($attendance_row['present_count'] > 0) ? 'Present' : 'Absent';
            $pdf->Cell($week_width, 10, $status, 1, 0, 'C');
        }
        $pdf->Ln();
    }
} else {
    $pdf->Cell(0, 10, 'No students found with the selected filters', 1, 1, 'C');
}

// Generate summary statistics
$pdf->AddPage();
$pdf->SetFont('helvetica', 'B', 14);
$pdf->Cell(0, 10, 'Attendance Summary - ' . $month_name . ' ' . $year, 0, 1, 'C');
$pdf->Ln(5);


$pdf->SetFont('helvetica', 'B', 12);
$pdf->Cell(0, 10, 'Weekly Attendance Summary', 0, 1, 'L');
$pdf->Ln(2);

$pdf->SetFont('helvetica', 'B', 10);
$pdf->Cell(50, 10, 'Week', 1, 0, 'C', 1);
$pdf->Cell(50, 10, 'Present Count', 1, 0, 'C', 1);
$pdf->Cell(50, 10, 'Absent Count', 1, 0, 'C', 1);
$pdf->Cell(50, 10, 'Attendance Rate', 1, 1, 'C', 1);

$pdf->SetFont('helvetica', '', 10);

$total_students_query = "
    SELECT COUNT(DISTINCT student_id) as total
    FROM students
";
if (!empty($grade_filter)) {
    $total_students_query .= " WHERE grade = '$grade_filter'";
}
$total_students_result = $conn->query($total_students_query);
$total_students_row = $total_students_result->fetch_assoc();
$total_students = $total_students_row['total'];

foreach ($weeks as $week_num => $week_dates) {
    $week_start = $week_dates['start'];
    $week_end = $week_dates['end'];

    
    $present_query = "
        SELECT COUNT(DISTINCT student_id) as present_count
        FROM attendance
        WHERE date BETWEEN '$week_start' AND '$week_end'
    ";
    if (!empty($subject_filter)) {
        $present_query .= " AND subject_name = '$subject_filter'";
    }
    if (!empty($grade_filter)) {
        $present_query .= " AND grade = '$grade_filter'";
    }
    $present_result = $conn->query($present_query);
    $present_row = $present_result->fetch_assoc();
    $present_count = $present_row['present_count'];

    $absent_count = $total_students - $present_count;
    $attendance_rate = ($total_students > 0) ? round(($present_count / $total_students) * 100, 2) : 0;

    $pdf->Cell(50, 10, 'Week ' . $week_num . ' (' . date('d/m', strtotime($week_start)) . '-' . date('d/m', strtotime($week_end)) . ')', 1, 0, 'L');
    $pdf->Cell(50, 10, $present_count, 1, 0, 'C');
    $pdf->Cell(50, 10, $absent_count, 1, 0, 'C');
    $pdf->Cell(50, 10, $attendance_rate . '%', 1, 1, 'C');
}

// Subject-wise attendance summary
if (empty($subject_filter)) {
    $pdf->Ln(10);
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell(0, 10, 'Subject-wise Attendance Summary', 0, 1, 'L');
    $pdf->Ln(2);

    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(80, 10, 'Subject', 1, 0, 'C', 1);
    $pdf->Cell(50, 10, 'Present Count', 1, 0, 'C', 1);
    $pdf->Cell(70, 10, 'Attendance Rate', 1, 1, 'C', 1);

    $pdf->SetFont('helvetica', '', 10);

    
    $subject_query = "
        SELECT subject_name, COUNT(DISTINCT student_id) as present_count
        FROM attendance
        WHERE MONTH(date) = $month AND YEAR(date) = $year
    ";
    if (!empty($grade_filter)) {
        $subject_query .= " AND grade = '$grade_filter'";
    }
    $subject_query .= " GROUP BY subject_name ORDER BY present_count DESC";

    $subject_result = $conn->query($subject_query);

    while ($subject_row = $subject_result->fetch_assoc()) {
        $subject_name = $subject_row['subject_name'];
        $present_count = $subject_row['present_count'];
        $attendance_rate = ($total_students > 0) ? round(($present_count / $total_students) * 100, 2) : 0;

        $pdf->Cell(80, 10, $subject_name, 1, 0, 'L');
        $pdf->Cell(50, 10, $present_count, 1, 0, 'C');
        $pdf->Cell(70, 10, $attendance_rate . '%', 1, 1, 'C');
    }
}


$pdf_filename = 'Attendance_Report_' . $month_name . '_' . $year . '.pdf';
$pdf->Output($pdf_filename, 'D');