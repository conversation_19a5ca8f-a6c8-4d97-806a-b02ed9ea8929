<?php
include_once 'server/db_connect.php';

header('Content-Type: application/json');

try {
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Query to get payment rate by subject
    $query = "SELECT 
                sub.subject_name,
                COUNT(s.student_id) as total_students,
                SUM(CASE WHEN s.payment_status = 'PAID' THEN 1 ELSE 0 END) as paid_students,
                ROUND((SUM(CASE WHEN s.payment_status = 'PAID' THEN 1 ELSE 0 END) / COUNT(s.student_id)) * 100, 1) as payment_rate
              FROM 
                students s
              JOIN 
                student_subjects ss ON s.student_id = ss.student_id
              JOIN 
                subjects sub ON ss.subject_id = sub.subject_id
              GROUP BY 
                sub.subject_id, sub.subject_name
              ORDER BY 
                payment_rate DESC";
    
    $result = $conn->query($query);
    
    if (!$result) {
        throw new Exception("Query failed: " . $conn->error);
    }
    
    $subjects = [];
    $rates = [];
    $colors = [];
    
    while ($row = $result->fetch_assoc()) {
        $subjects[] = $row['subject_name'];
        $rates[] = (float)$row['payment_rate'];
        
        // Generate color based on payment rate (green for high, red for low)
        $rate = (float)$row['payment_rate'];
        if ($rate >= 75) {
            $colors[] = 'rgba(40, 167, 69, 0.7)'; // Green
        } elseif ($rate >= 50) {
            $colors[] = 'rgba(255, 193, 7, 0.7)'; // Yellow
        } else {
            $colors[] = 'rgba(220, 53, 69, 0.7)'; // Red
        }
    }
    
    echo json_encode([
        'subjects' => $subjects,
        'rates' => $rates,
        'colors' => $colors
    ]);
    
} catch (Exception $e) {
    error_log("Error in fetch_subject_payment_data.php: " . $e->getMessage());
    echo json_encode(['error' => $e->getMessage()]);
}
?>