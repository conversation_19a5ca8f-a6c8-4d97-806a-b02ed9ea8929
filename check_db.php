<?php
include_once 'backend/server/db_connect.php';

echo "<h1>Database Tables Check</h1>";

// Check teacher_subjects table
echo "<h2>Teacher Subjects Table</h2>";
$result = $conn->query("SELECT * FROM teacher_subjects LIMIT 10");
if ($result) {
    echo "<table border='1'>";
    echo "<tr>";
    $fields = $result->fetch_fields();
    foreach ($fields as $field) {
        echo "<th>" . $field->name . "</th>";
    }
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . $value . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error querying teacher_subjects table: " . $conn->error;
}

// Check subjects table
echo "<h2>Subjects Table</h2>";
$result = $conn->query("SELECT * FROM subjects LIMIT 10");
if ($result) {
    echo "<table border='1'>";
    echo "<tr>";
    $fields = $result->fetch_fields();
    foreach ($fields as $field) {
        echo "<th>" . $field->name . "</th>";
    }
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . $value . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error querying subjects table: " . $conn->error;
}

// Check teachers table
echo "<h2>Teachers Table</h2>";
$result = $conn->query("SELECT * FROM teachers LIMIT 10");
if ($result) {
    echo "<table border='1'>";
    echo "<tr>";
    $fields = $result->fetch_fields();
    foreach ($fields as $field) {
        echo "<th>" . $field->name . "</th>";
    }
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . $value . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error querying teachers table: " . $conn->error;
}

$conn->close();
?>
