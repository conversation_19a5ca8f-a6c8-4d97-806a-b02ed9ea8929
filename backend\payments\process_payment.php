<?php

session_start();


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'parent') {
    header("Location: ../../backend/server/login.php");
    exit();
}

require '../../vendor/autoload.php';
include_once '../server/db_connect.php';

// Make sure Stripe namespace is available
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Exception\ApiErrorException;


error_reporting(E_ALL);
ini_set('display_errors', 1);


if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: ../../frontend/payments/parent_payment.php');
    exit();
}

// Get  data
$student_id = $_POST['student_id'];
$subject_id = $_POST['subject_id'];
$payment_period = $_POST['payment_period'];
$amount = $_POST['amount'];
$notes = isset($_POST['notes']) ? $_POST['notes'] : '';


echo "<pre>Form data: ";
print_r($_POST);
echo "</pre>";

// Validate amount
if ($amount < 500) {
    echo "<script>alert('Amount must be at least 500 LKR'); window.history.back();</script>";
    exit();
}

try {
    // Validate student exists
    $student_query = $conn->prepare("
        SELECT s.student_id, u.first_name, u.last_name
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE s.student_id = ?
    ");
    $student_query->bind_param("i", $student_id);
    $student_query->execute();
    $student_result = $student_query->get_result();

  
    echo "<pre>Student query result: ";
    print_r($student_result->num_rows);
    echo "</pre>";

    if ($student_result->num_rows === 0) {
        echo "<script>alert('Invalid student ID'); window.history.back();</script>";
        exit();
    }

    $student = $student_result->fetch_assoc();
    $student_name = $student['first_name'] . ' ' . $student['last_name'];

    
    echo "<pre>Student name: $student_name</pre>";

    // Validate subject exists
    $subject_query = $conn->prepare("SELECT subject_name FROM subjects WHERE subject_id = ?");
    $subject_query->bind_param("i", $subject_id);
    $subject_query->execute();
    $subject_result = $subject_query->get_result();

    // Print subject query result
    echo "<pre>Subject query result: ";
    print_r($subject_result->num_rows);
    echo "</pre>";

    if ($subject_result->num_rows === 0) {
        echo "<script>alert('Invalid subject'); window.history.back();</script>";
        exit();
    }

    $subject = $subject_result->fetch_assoc();
    $subject_name = $subject['subject_name'];

   
    echo "<pre>Subject name: $subject_name</pre>";

    // Extract year and month from payment period
    list($payment_year, $payment_month) = explode('-', $payment_period);
    $month_name = date('F', mktime(0, 0, 0, $payment_month, 1));

    
    echo "<pre>Payment period: $month_name $payment_year</pre>";

    // Initialize Stripe with test API key
    $stripe_key = 'sk_test_51RLOlhFjMkGP5dsLeAmPlAvrGl7oNq5gvqijfgtFIjBYfbfgKsmvv3utPbL3z2V3FDtEgfaCry3ATh0dTTRiZfPP00vUkW9DW9';
    Stripe::setApiKey($stripe_key);

    echo "<pre>Creating Stripe checkout session...</pre>";

    // Create a Checkout Session
    $checkout_session = Session::create([
        'payment_method_types' => ['card'],
        'line_items' => [[
            'price_data' => [
                'currency' => 'lkr',
                'product_data' => [
                    'name' => 'Tuition Fee - ' . $subject_name,
                    'description' => 'Student: ' . $student_name . ' (ID: ' . $student_id . ') | Period: ' . $month_name . ' ' . $payment_year,
                ],
                'unit_amount' => $amount * 100, // Convert to cents
            ],
            'quantity' => 1,
        ]],
        'mode' => 'payment',
        'success_url' => 'http://' . $_SERVER['HTTP_HOST'] . '/gurukula_lms/frontend/payments/payment_success.php?session_id={CHECKOUT_SESSION_ID}',
        'cancel_url' => 'http://' . $_SERVER['HTTP_HOST'] . '/gurukula_lms/frontend/payments/parent_payment.php',
        'metadata' => [
            'student_id' => $student_id,
            'student_name' => $student_name,
            'subject_id' => $subject_id,
            'subject_name' => $subject_name,
            'payment_period' => $payment_period,
            'payment_year' => $payment_year,
            'payment_month' => $payment_month,
            'notes' => $notes
        ],
    ]);

    echo "<pre>Checkout session created: " . $checkout_session->id . "</pre>";

    // Save payment request to database
    try {
        
        $stmt = $conn->prepare("INSERT INTO payment_requests
                               (student_id, subject_id, amount, status, stripe_session_id)
                               VALUES (?, ?, ?, 'PENDING', ?)");
        $stmt->bind_param("iiis", $student_id, $subject_id, $amount, $checkout_session->id);
        $stmt->execute();

        echo "<pre>Payment request saved to database</pre>";
        echo "<pre>Redirecting to: " . $checkout_session->url . "</pre>";

        // Redirect to Stripe checkout
        header("Location: " . $checkout_session->url);
        exit();

    } catch (\Exception $e) {
       
        echo "<pre>Error saving to database: " . $e->getMessage() . "</pre>";

        // Even if database save fails, still redirect to Stripe
        echo "<pre>Continuing with Stripe checkout anyway...</pre>";
        echo "<pre>Redirecting to: " . $checkout_session->url . "</pre>";

        // Redirect to Stripe checkout
        header("Location: " . $checkout_session->url);
        exit();
    }

} catch (\Exception $e) {
   
    echo "<pre>Error: " . $e->getMessage() . "</pre>";
    echo "<script>alert('Error: " . $e->getMessage() . "'); window.history.back();</script>";
    exit();
}
?>




