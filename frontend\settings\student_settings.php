<?php
session_start();
include('../../backend/server/db_connect.php');
include_once '../../assets/student_sidebar.php';

// Check if user is logged in and is a student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header('Location: ../../frontend/login.php');
    exit;
}

// Get student information
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("
    SELECT s.student_id, s.phone, u.first_name, u.last_name, u.email
    FROM students s
    JOIN users u ON s.user_id = u.id
    WHERE s.user_id = ?
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

// Set default values for notification preferences
$email_notifications = 0;
$popup_notifications = 0;

// Get active tab from URL or default to profile
$active_tab = isset($_GET['tab']) ? $_GET['tab'] : 'profile';
?>

<!DOCTYPE html>
<html>
<head>
  <title>Student Settings</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      --border-radius: 12px;
      --transition-speed: 0.3s;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--light-bg);
      color: var(--dark-text);
    }

    .settings-container {
      margin-left: 290px;
      padding: 30px;
    }

    .page-header {
      margin-bottom: 30px;
    }

    .page-header h1 {
      font-weight: 600;
      color: var(--dark-text);
      margin-bottom: 10px;
    }

    .settings-wrapper {
      display: flex;
      gap: 30px;
    }

    .settings-menu {
      flex: 0 0 300px;
    }

    .settings-content {
      flex: 1;
    }

    .nav-pills .nav-link {
      color: var(--dark-text);
      border-radius: var(--border-radius);
      padding: 12px 20px;
      margin-bottom: 10px;
      transition: all var(--transition-speed) ease;
      display: flex;
      align-items: center;
      font-weight: 500;
    }

    .nav-pills .nav-link i {
      margin-right: 12px;
      font-size: 1.1rem;
    }

    .nav-pills .nav-link.active {
      background-color: var(--primary-color);
      color: white;
      box-shadow: var(--card-shadow);
    }

    .nav-pills .nav-link:hover:not(.active) {
      background-color: rgba(67, 97, 238, 0.1);
      transform: translateX(5px);
    }

    .card {
      border: none;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      transition: all var(--transition-speed) ease;
      overflow: hidden;
    }

    .card:hover {
      box-shadow: var(--hover-shadow);
    }

    .card-header {
      background-color: white;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      padding: 20px;
      font-weight: 600;
    }

    .card-body {
      padding: 25px;
    }

    .form-label {
      font-weight: 500;
      color: var(--dark-text);
      margin-bottom: 8px;
    }

    .form-control {
      border-radius: 8px;
      padding: 12px 15px;
      border: 1px solid #e2e8f0;
      transition: all 0.2s ease;
    }

    .form-control:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
    }

    .btn-primary {
      background-color: var(--primary-color);
      border: none;
      border-radius: 8px;
      padding: 12px 25px;
      font-weight: 500;
      transition: all 0.2s ease;
    }

    .btn-primary:hover {
      background-color: var(--secondary-color);
      transform: translateY(-2px);
    }

    .list-group-item {
      border: none;
      padding: 15px;
      border-radius: 8px !important;
      margin-bottom: 8px;
      background-color: #f8fafc;
    }

    .subject-card {
      border-radius: 10px;
      margin-bottom: 15px;
      transition: all 0.3s ease;
    }

    .subject-card:hover {
      transform: translateY(-3px);
    }

    .subject-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px 20px;
      border-radius: 10px 10px 0 0;
      background-color: #f8f9fa;
      border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .subject-name {
      font-weight: 600;
      font-size: 1.1rem;
      display: flex;
      align-items: center;
    }

    .subject-name i {
      margin-right: 10px;
      color: var(--primary-color);
    }

    .subject-body {
      padding: 15px 20px;
    }

    .class-time {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      color: var(--light-text);
    }

    .class-time i {
      margin-right: 10px;
      color: var(--accent-color);
    }

    @media (max-width: 992px) {
      .settings-wrapper {
        flex-direction: column;
      }

      .settings-menu {
        flex: 0 0 100%;
      }
    }

    @media (max-width: 768px) {
      .settings-container {
        margin-left: 0;
        padding: 15px;
      }
    }
  </style>
</head>
<body>

<div class="settings-container">
  <div class="page-header">
    <h1>Account Settings</h1>
    <p class="text-muted">Manage your account preferences and information</p>
  </div>

  <div class="settings-wrapper">
    <div class="settings-menu">
      <div class="card">
        <div class="card-body p-0">
        <div class="nav flex-column nav-pills">
            <a href="student_settings.php?tab=profile" class="nav-link <?php echo $active_tab == 'profile' ? 'active' : ''; ?>">
              <i class="fas fa-user-circle"></i> Profile Information
            </a>
            <a href="student_settings.php?tab=password" class="nav-link <?php echo $active_tab == 'password' ? 'active' : ''; ?>">
              <i class="fas fa-lock"></i> Change Password
            </a>
            <a href="student_settings.php?tab=notifications" class="nav-link <?php echo $active_tab == 'notifications' ? 'active' : ''; ?>">
              <i class="fas fa-bell"></i> Class Notification
            </a>
          </div>
          </div>

      </div>
    </div>

    <div class="settings-content">
      <?php if ($active_tab == 'profile'): ?>
        <!-- Profile Form -->
        <div class="card">
          <div class="card-header d-flex align-items-center">
            <i class="fas fa-user-circle me-2 text-primary"></i>
            Profile Information
          </div>
          <div class="card-body">
            <?php if (isset($_SESSION['success'])): ?>
              <div class="alert alert-success d-flex align-items-center" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <div><?= $_SESSION['success'] ?></div>
              </div>
              <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
              <div class="alert alert-danger d-flex align-items-center" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <div><?= $_SESSION['error'] ?></div>
              </div>
              <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <form action="../../backend/settings/update_student_profile.php" method="POST" id="profileForm">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="first_name" class="form-label">First Name</label>
                  <input type="text" class="form-control" name="first_name" id="first_name" value="<?= htmlspecialchars($student['first_name'] ?? '') ?>" required>
                </div>

                <div class="col-md-6 mb-3">
                  <label for="last_name" class="form-label">Last Name</label>
                  <input type="text" class="form-control" name="last_name" id="last_name" value="<?= htmlspecialchars($student['last_name'] ?? '') ?>" required>
                </div>
              </div>

              <div class="mb-3">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" class="form-control" name="email" id="email" value="<?= htmlspecialchars($student['email'] ?? '') ?>" required>
              </div>

              <div class="mb-3">
                <label for="phone" class="form-label">Phone Number</label>
                <input type="tel" class="form-control" name="phone" id="phone" value="<?= htmlspecialchars($student['phone'] ?? '') ?>" placeholder="Optional">
              </div>

              <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save me-2"></i>Save Changes
                </button>
              </div>
            </form>
          </div>
        </div>
      <?php elseif ($active_tab == 'password'): ?>
        <!-- Change Password Form -->
        <div class="card">
          <div class="card-header d-flex align-items-center">
            <i class="fas fa-lock me-2 text-primary"></i>
            Change Password
          </div>
          <div class="card-body">
            <?php if (isset($_SESSION['success'])): ?>
              <div class="alert alert-success d-flex align-items-center" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <div><?= $_SESSION['success'] ?></div>
              </div>
              <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
              <div class="alert alert-danger d-flex align-items-center" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <div><?= $_SESSION['error'] ?></div>
              </div>
              <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <form action="../../backend/settings/change_password.php" method="POST" id="passwordForm">
              <div class="mb-3 position-relative">
                <label for="current_password" class="form-label">Current Password</label>
                <div class="input-group">
                  <input type="password" class="form-control" name="current_password" id="current_password" required>
                  <button class="btn btn-outline-secondary password-toggle" type="button" tabindex="-1">
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
              </div>

              <div class="mb-3 position-relative">
                <label for="new_password" class="form-label">New Password</label>
                <div class="input-group">
                  <input type="password" class="form-control" name="new_password" id="new_password" required>
                  <button class="btn btn-outline-secondary password-toggle" type="button" tabindex="-1">
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
                <div class="password-strength mt-2">
                  <div class="progress" style="height: 5px;">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                  </div>
                  <small class="text-muted strength-text mt-1 d-inline-block">Password must be at least 8 characters with uppercase, lowercase, numbers, and symbols</small>
                </div>
              </div>

              <div class="mb-4 position-relative">
                <label for="confirm_password" class="form-label">Confirm New Password</label>
                <div class="input-group">
                  <input type="password" class="form-control" name="confirm_password" id="confirm_password" required>
                  <button class="btn btn-outline-secondary password-toggle" type="button" tabindex="-1">
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
                <div id="passwordMatch" class="form-text mt-2"></div>
              </div>

              <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-key me-2"></i> Change Password
                </button>
              </div>
            </form>
          </div>
        </div>
      <?php elseif ($active_tab == 'notifications'): ?>
        <!-- Notification Preferences -->
        <div class="card">
          <div class="card-header d-flex align-items-center">
            <i class="fas fa-bell me-2 text-primary"></i>
            Class Notification Preferences
          </div>
          <div class="card-body">
            <?php if (isset($_SESSION['success'])): ?>
              <div class="alert alert-success d-flex align-items-center" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <div><?= $_SESSION['success'] ?></div>
              </div>
              <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
              <div class="alert alert-danger d-flex align-items-center" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <div><?= $_SESSION['error'] ?></div>
              </div>
              <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <form action="../../backend/settings/update_student_notifications.php" method="POST">
              <div class="mb-4">
                <h5 class="mb-3">Class Reminders</h5>


                <div class="form-check form-switch mb-3">
                  <input type="checkbox" class="form-check-input" id="popup_notifications" name="popup_notifications"
                    <?php echo ($popup_notifications == 1) ? 'checked' : ''; ?>>
                  <label class="form-check-label" for="popup_notifications">
                    <i class="fas fa-bell me-2 text-primary"></i> Enable Class Notifications
                  </label>
                  <div class="form-text">Receive popup notifications about upcoming classes when you're logged in</div>
                </div>
              </div>

              <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save me-2"></i>Save Notification Settings
                </button>
              </div>
            </form>
          </div>
        </div>
      <?php endif; ?>
    </div>
  </div>
</div>
</body>
</html>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    const toggleButtons = document.querySelectorAll('.password-toggle');
    toggleButtons.forEach(button => {
      button.addEventListener('click', function() {
        const input = this.previousElementSibling;
        const icon = this.querySelector('i');

        if (input.type === 'password') {
          input.type = 'text';
          icon.classList.remove('fa-eye');
          icon.classList.add('fa-eye-slash');
        } else {
          input.type = 'password';
          icon.classList.remove('fa-eye-slash');
          icon.classList.add('fa-eye');
        }
      });
    });

    // Password validation
    const passwordForm = document.getElementById('passwordForm');
    if (passwordForm) {
      const newPassword = document.getElementById('new_password');
      const confirmPassword = document.getElementById('confirm_password');
      const passwordStrength = document.querySelector('.password-strength');
      const progressBar = document.querySelector('.password-strength .progress-bar');
      const strengthText = document.querySelector('.password-strength .strength-text');
      const passwordMatch = document.getElementById('passwordMatch');
      const submitButton = passwordForm.querySelector('button[type="submit"]');

      // Password strength validation
      newPassword.addEventListener('input', function() {
        const password = this.value;
        passwordStrength.classList.remove('d-none');

        // Check password strength
        let strength = 0;
        let feedback = [];

        // Length check
        if (password.length >= 8) {
          strength += 25;
        } else {
          feedback.push('At least 8 characters');
        }

        // Uppercase check
        if (/[A-Z]/.test(password)) {
          strength += 25;
        } else {
          feedback.push('At least one uppercase letter');
        }

        // Lowercase check
        if (/[a-z]/.test(password)) {
          strength += 25;
        } else {
          feedback.push('At least one lowercase letter');
        }

        // Number and symbol check
        if (/[0-9]/.test(password) && /[^A-Za-z0-9]/.test(password)) {
          strength += 25;
        } else {
          feedback.push('At least one number and one symbol');
        }

        // Update progress bar
        progressBar.style.width = strength + '%';

        // Update color based on strength
        if (strength < 50) {
          progressBar.className = 'progress-bar bg-danger';
          strengthText.textContent = 'Weak password: ' + feedback.join(', ');
        } else if (strength < 100) {
          progressBar.className = 'progress-bar bg-warning';
          strengthText.textContent = 'Medium password: ' + feedback.join(', ');
        } else {
          progressBar.className = 'progress-bar bg-success';
          strengthText.textContent = 'Strong password';
        }

        // Check if passwords match
        if (confirmPassword.value) {
          checkPasswordsMatch();
        }
      });

      // Check if passwords match
      function checkPasswordsMatch() {
        if (newPassword.value === confirmPassword.value) {
          passwordMatch.innerHTML = '<span class="text-success"><i class="fas fa-check-circle me-1"></i>Passwords match</span>';
          return true;
        } else {
          passwordMatch.innerHTML = '<span class="text-danger"><i class="fas fa-times-circle me-1"></i>Passwords do not match</span>';
          return false;
        }
      }

      confirmPassword.addEventListener('input', checkPasswordsMatch);

      // Form submission validation
      passwordForm.addEventListener('submit', function(e) {
        const password = newPassword.value;
        const isValid =
          password.length >= 8 &&
          /[A-Z]/.test(password) &&
          /[a-z]/.test(password) &&
          /[0-9]/.test(password) &&
          /[^A-Za-z0-9]/.test(password) &&
          checkPasswordsMatch();

        if (!isValid) {
          e.preventDefault();
          alert('Please ensure your password is at least 8 characters long and contains uppercase letters, lowercase letters, numbers, and symbols.');
        }
      });
    }
  });
</script>




