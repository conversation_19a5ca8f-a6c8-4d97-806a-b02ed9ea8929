<?php
session_start();
// Fix the include path based on how the script is called
$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';

// Check if user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../frontend/login.php");
    exit();
}

// Get teacher ID
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT t.teacher_id, u.first_name, u.last_name
                        FROM teachers t
                        JOIN users u ON t.user_id = u.id
                        WHERE t.user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();

if (!$teacher) {
    echo "Teacher not found.";
    exit();
}

$teacher_id = $teacher['teacher_id'];
$teacher_name = $teacher['first_name'] . ' ' . $teacher['last_name'];

// Process form submission
$message = "";
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Create a test notification
    $notification_text = $_POST['notification_text'] ?? "⚠️ IMPORTANT: You have a class for Grade 10 (Mathematics) tomorrow at 09:00 AM.";

    $stmt = $conn->prepare("INSERT INTO notifications (teacher_id, message, scheduled_time, is_read) VALUES (?, ?, NOW(), 0)");
    $stmt->bind_param("is", $teacher_id, $notification_text);

    if ($stmt->execute()) {
        $message = "<div class='alert alert-success'>Notification created successfully!</div>";
    } else {
        $message = "<div class='alert alert-danger'>Error creating notification: " . $conn->error . "</div>";
    }
}

// Get current notifications
$stmt = $conn->prepare("SELECT id, message, created_at, scheduled_time, is_read FROM notifications WHERE teacher_id = ? ORDER BY scheduled_time DESC LIMIT 10");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$notifications = $stmt->get_result();

// HTML output
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Class Reminder</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f4f6f9;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(to right, #4361ee, #4895ef);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            font-weight: 600;
        }
        .btn-primary {
            background-color: #4361ee;
            border-color: #4361ee;
        }
        .btn-primary:hover {
            background-color: #3f37c9;
            border-color: #3f37c9;
        }
        .notification-item {
            border-left: 4px solid #4361ee;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .notification-item.unread {
            border-left-color: #ffc107;
            background-color: #fff8e1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Manual Class Reminder</h1>

        <?php echo $message; ?>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-plus-circle me-2"></i>Create Test Notification
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <div class="mb-3">
                                <label for="notification_text" class="form-label">Notification Text</label>
                                <textarea class="form-control" id="notification_text" name="notification_text" rows="3">⚠️ IMPORTANT: You have a class for Grade 10 (Mathematics) tomorrow at 09:00 AM.</textarea>
                                <div class="form-text">This will create a notification for teacher: <?php echo htmlspecialchars($teacher_name); ?></div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-bell me-2"></i>Create Notification
                            </button>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-cog me-2"></i>Notification Tools
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="check_reminders.php" class="btn btn-primary">
                                <i class="fas fa-sync-alt me-2"></i>Run Reminder Check Now
                            </a>
                            <a href="cron_setup.php" class="btn btn-secondary">
                                <i class="fas fa-cog me-2"></i>Setup Automatic Reminders
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-bell me-2"></i>Current Notifications
                    </div>
                    <div class="card-body">
                        <?php if ($notifications->num_rows > 0): ?>
                            <?php while ($notification = $notifications->fetch_assoc()): ?>
                                <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>">
                                    <div class="d-flex justify-content-between">
                                        <strong><?php echo htmlspecialchars($notification['message']); ?></strong>
                                        <?php if (!$notification['is_read']): ?>
                                            <span class="badge bg-warning">Unread</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-muted small mt-1">
                                        <?php
                                        $time = $notification['scheduled_time'] ?: $notification['created_at'];
                                        echo date('M j, Y g:i A', strtotime($time));
                                        ?>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <p class="text-center py-3">No notifications found.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <a href="../../frontend/timetables/teacher_timetable.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Timetable
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
