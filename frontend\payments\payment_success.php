<?php

session_start();


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'parent') {
    header("Location: ../../backend/server/login.php");
    exit();
}

require '../../vendor/autoload.php';
include_once '../../backend/server/db_connect.php';


if (!isset($_GET['session_id'])) {
    header("Location: parent_payment.php");
    exit();
}

$session_id = $_GET['session_id'];

// Initialize Stripe with test API key
$stripe_key = 'sk_test_51RLOlhFjMkGP5dsLeAmPlAvrGl7oNq5gvqijfgtFIjBYfbfgKsmvv3utPbL3z2V3FDtEgfaCry3ATh0dTTRiZfPP00vUkW9DW9';
\Stripe\Stripe::setApiKey($stripe_key);

try {
    // Retrieve the checkout session,payment intent
    $session = \Stripe\Checkout\Session::retrieve($session_id);

  
    $payment_intent = \Stripe\PaymentIntent::retrieve($session->payment_intent);

    // Get payment details
    $amount = $payment_intent->amount / 100;                                                  // Convert from cents
    $currency = strtoupper($payment_intent->currency);
    $status = $payment_intent->status;
    $created = date('M d, Y H:i:s', $payment_intent->created);

    
    $student_id = $session->metadata->student_id;
    $student_name = $session->metadata->student_name;
    $subject_name = $session->metadata->subject_name;
    $payment_period = $session->metadata->payment_period;

    // Format payment period for display
    list($year, $month) = explode('-', $payment_period);
    $month_name = date('F', mktime(0, 0, 0, $month, 1));
    $formatted_period = $month_name . ' ' . $year;

    // Calculate next payment due date 
    $next_month = $month + 1;
    $next_year = $year;
    if ($next_month > 12) {
        $next_month = 1;
        $next_year++;
    }
    $next_payment_due = $next_year . '-' . str_pad($next_month, 2, '0', STR_PAD_LEFT) . '-01';

    // Update payment status in database
    $update_payment = $conn->prepare("UPDATE payment_requests
                                     SET status='PAID', payment_method='Card', payment_date=NOW()
                                     WHERE stripe_session_id=?");
    $update_payment->bind_param("s", $session_id);
    $update_payment->execute();

    // Update student payment status
    $update_student = $conn->prepare("UPDATE students
                                     SET payment_status='PAID',
                                         last_payment_date=NOW(),
                                         last_payment_period=?,
                                         next_payment_due=?
                                     WHERE student_id=?");
    $update_student->bind_param("ssi", $payment_period, $next_payment_due, $student_id);
    $update_student->execute();

    // Update student_subjects table
    $check_subject = $conn->prepare("SELECT * FROM student_subjects WHERE student_id=? AND subject_id=?");
    $check_subject->bind_param("ii", $student_id, $session->metadata->subject_id);
    $check_subject->execute();
    $result = $check_subject->get_result();

    if ($result->num_rows > 0) {
        // Update existing record
        $update_subject = $conn->prepare("UPDATE student_subjects
                                         SET payment_status='PAID',
                                             last_payment_date=NOW(),
                                             last_payment_period=?
                                         WHERE student_id=? AND subject_id=?");
        $update_subject->bind_param("sii", $payment_period, $student_id, $session->metadata->subject_id);
        $update_subject->execute();
    } else {
        
        $insert_subject = $conn->prepare("INSERT INTO student_subjects
                                         (student_id, subject_id, payment_status, last_payment_date, last_payment_period)
                                         VALUES (?, ?, 'PAID', NOW(), ?)");
        $insert_subject->bind_param("iis", $student_id, $session->metadata->subject_id, $payment_period);
        $insert_subject->execute();
    }

    
    $update_payment->close();
    $update_student->close();
    $check_subject->close();
    $conn->close();
}
catch (\Stripe\Exception\ApiErrorException $e) {
    // Handle error
    echo 'Error: ' . $e->getMessage();
    exit();
}
?>


<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment Successful</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
    }
    body {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
    }
    .container {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      padding: 15px;
    }
    .success-card {
      width: 100%;
      max-width: 500px;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      overflow: hidden;
      margin: 0 auto;
    }
    .success-header {
      background-color: #28a745;
      color: white;
      padding: 30px 0;
      text-align: center;
    }
    .success-icon {
      font-size: 60px;
      margin-bottom: 10px;
    }
    .btn-return {
      background-color: #28a745;
      border-color: #28a745;
    }
    .btn-return:hover {
      background-color: #218838;
      border-color: #1e7e34;
    }
    .btn-download {
      background-color: #007bff;
      border-color: #007bff;
      margin-bottom: 10px;
    }
    .btn-download:hover {
      background-color: #0069d9;
      border-color: #0062cc;
    }
    .payment-details {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
      text-align: left;
    }
    .payment-details p {
      margin-bottom: 8px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="card success-card">
      <div class="success-header">
        <i class="fas fa-check-circle success-icon"></i>
        <h2>Payment Successful!</h2>
      </div>
      <div class="card-body p-4">
        <div class="payment-details">
          <p><strong>Student:</strong> <span id="student-name"><?php echo htmlspecialchars($student_name); ?></span></p>
          <p><strong>Subject:</strong> <span id="subject-name"><?php echo htmlspecialchars($subject_name); ?></span></p>
          <p><strong>Period:</strong> <span id="payment-period"><?php echo htmlspecialchars($formatted_period); ?></span></p>
          <p><strong>Amount:</strong> <span id="payment-amount"><?php echo htmlspecialchars($currency); ?> <?php echo htmlspecialchars(number_format($amount, 2)); ?></span></p>
          <p><strong>Date:</strong> <span id="payment-date"><?php echo htmlspecialchars($created); ?></span></p>
          <p><strong>Status:</strong> <span class="badge bg-success">Paid</span></p>
          <p><strong>Transaction ID:</strong> <span id="transaction-id"><?php echo htmlspecialchars($payment_intent->id); ?></span></p>
        </div>
        <div class="text-center">
          <p class="mb-4">Thank you for your payment. Your transaction has been completed successfully.</p>
          <button id="downloadBtn" class="btn btn-download btn-lg w-100 mb-3">
            <i class="fas fa-download me-2"></i> Download Receipt
          </button>
          <a href="../../frontend/dashboards/parent_dashboard.php" class="btn btn-return btn-lg w-100">Return to Dashboard</a>
        </div>
      </div>
    </div>
  </div>

 
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    
    window.jsPDF = window.jspdf.jsPDF;

    document.getElementById('downloadBtn').addEventListener('click', function() {
      // Create new PDF document
      const doc = new jsPDF();

      
      const studentName = document.getElementById('student-name').textContent;
      const subjectName = document.getElementById('subject-name').textContent;
      const paymentPeriod = document.getElementById('payment-period').textContent;
      const paymentAmount = document.getElementById('payment-amount').textContent;
      const paymentDate = document.getElementById('payment-date').textContent;
      const transactionId = document.getElementById('transaction-id').textContent;

      // header 
      doc.setFontSize(22);
      doc.setTextColor(40, 167, 69); // Green color
      doc.text('Payment Receipt', 105, 20, { align: 'center' });

      
      doc.setFontSize(16);
      doc.setTextColor(0, 0, 0);
      doc.text('GURUKULA Institution', 105, 30, { align: 'center' });

      // Add horizontal line
      doc.setDrawColor(40, 167, 69);
      doc.setLineWidth(0.5);
      doc.line(20, 35, 190, 35);

      // Add payment details
      doc.setFontSize(12);
      doc.text('Payment Details:', 20, 45);

      doc.setFontSize(10);
      doc.text('Student Name:', 20, 55);
      doc.text(studentName, 80, 55);

      doc.text('Subject:', 20, 65);
      doc.text(subjectName, 80, 65);

      doc.text('Payment Period:', 20, 75);
      doc.text(paymentPeriod, 80, 75);

      doc.text('Amount Paid:', 20, 85);
      doc.text(paymentAmount, 80, 85);

      doc.text('Payment Date:', 20, 95);
      doc.text(paymentDate, 80, 95);

      doc.text('Transaction ID:', 20, 105);
      doc.text(transactionId, 80, 105);

      doc.text('Payment Status:', 20, 115);
      doc.text('PAID', 80, 115);

      
      doc.setFontSize(12);
      doc.text('Thank you for your payment!', 105, 135, { align: 'center' });

     
      doc.setFontSize(8);
      doc.text('This is an electronically generated receipt and does not require a signature.', 105, 150, { align: 'center' });
      doc.text('For any queries, please contact us: 071 6941274', 105, 155, { align: 'center' });

      // Add current date at bottom
      const today = new Date().toLocaleDateString();
      doc.text('Generated on: ' + today, 20, 180);

      // Save PDF
      doc.save('Payment_Receipt_' + transactionId.substring(3, 10) + '.pdf');
    });
  </script>
</body>
</html>
