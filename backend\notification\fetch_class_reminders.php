<?php
session_start();
// Fix the include path based on how the script is called
$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';

// Add debugging
error_log("Fetching class reminders for user");

header('Content-Type: application/json');

$user_id = $_SESSION['user_id'] ?? 0;
$role = $_SESSION['role'] ?? '';

error_log("User ID: $user_id, Role: $role");

if ($role !== 'teacher') {
    error_log("Not a teacher, returning empty array");
    echo json_encode([]);
    exit();
}

// Get teacher ID
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$res = $stmt->get_result();
$teacher = $res->fetch_assoc();
$teacher_id = $teacher['teacher_id'] ?? 0;

error_log("Teacher ID: $teacher_id");

$data = [];
if ($teacher_id) {
    // Check if notifications table exists
    $tableExists = false;
    $result = $conn->query("SHOW TABLES LIKE 'notifications'");
    if ($result->num_rows > 0) {
        $tableExists = true;
    }

    if (!$tableExists) {
        // If notifications table doesn't exist, create it
        $sql = "CREATE TABLE IF NOT EXISTS notifications (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            teacher_id INT(11) NOT NULL,
            message VARCHAR(255) NOT NULL,
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            scheduled_time TIMESTAMP NULL
        )";

        if ($conn->query($sql) === TRUE) {
            error_log("Notifications table created successfully");
        } else {
            error_log("Error creating notifications table: " . $conn->error);
        }
    }

    // Get only class reminders (messages containing "class" and either "tomorrow" or "today")
    $q = $conn->prepare("
        SELECT id, message, created_at, scheduled_time, is_read 
        FROM notifications 
        WHERE teacher_id = ? 
        AND (
            (message LIKE '%class%' AND message LIKE '%tomorrow%') 
            OR 
            (message LIKE '%class%' AND message LIKE '%today%')
        )
        ORDER BY scheduled_time DESC, created_at DESC
        LIMIT 20
    ");
    $q->bind_param("i", $teacher_id);
    $q->execute();
    $r = $q->get_result();

    error_log("Found " . $r->num_rows . " class reminders");

    while ($row = $r->fetch_assoc()) {
        $data[] = $row;
        error_log("Class reminder: " . $row['message']);
    }

    // If no reminders found, check if there are any classes today or tomorrow
    if (count($data) == 0) {
        error_log("No class reminders found, checking timetable");
        
        // Get current day and tomorrow
        $today = date('l'); // e.g., "Monday"
        $tomorrow = date('l', strtotime('+1 day')); // Tomorrow's day name
        
        // Check for classes today and tomorrow
        $timetableQuery = $conn->prepare("
            SELECT tt.*, s.subject_name 
            FROM teacher_timetable tt
            LEFT JOIN subjects s ON tt.subject_id = s.subject_id
            WHERE tt.teacher_id = ? 
            AND (tt.day_of_week = ? OR tt.day_of_week = ?)
            ORDER BY FIELD(tt.day_of_week, ?, ?), tt.start_time
        ");
        $timetableQuery->bind_param("issss", $teacher_id, $today, $tomorrow, $today, $tomorrow);
        $timetableQuery->execute();
        $timetableResult = $timetableQuery->get_result();
        
        error_log("Found " . $timetableResult->num_rows . " classes for today/tomorrow");
        
        if ($timetableResult->num_rows > 0) {
            // Create a message about upcoming classes
            $message = "You have " . $timetableResult->num_rows . " class(es) scheduled for today and tomorrow. Check your timetable.";
            
            $data[] = [
                'id' => 0,
                'message' => $message,
                'created_at' => date('Y-m-d H:i:s'),
                'scheduled_time' => null,
                'is_read' => 0,
                'is_system_message' => true
            ];
        }
    }
}

echo json_encode($data);
?>
