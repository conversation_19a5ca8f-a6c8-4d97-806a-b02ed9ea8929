<?php

session_start();


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'parent') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit();
}


include_once '../../backend/server/db_connect.php';


header('Content-Type: application/json');


if (!isset($_POST['subject_id']) || empty($_POST['subject_id'])) {
    echo json_encode(['success' => false, 'message' => 'Subject ID is required']);
    exit;
}


if (!isset($_POST['grade']) || empty($_POST['grade'])) {
    echo json_encode(['success' => false, 'message' => 'Grade is required']);
    exit;
}

$subject_id = $_POST['subject_id'];
$grade = $_POST['grade'];


$stmt = $conn->prepare("SELECT fee FROM subject_fees WHERE subject_id = ? AND grade = ?");
$stmt->bind_param("is", $subject_id, $grade);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    echo json_encode(['success' => true, 'fee' => $row['fee']]);
} else {
    // If no specific fee is found for this grade, use the default fee from subjects table
    $default_stmt = $conn->prepare("SELECT fee FROM subjects WHERE subject_id = ?");
    $default_stmt->bind_param("i", $subject_id);
    $default_stmt->execute();
    $default_result = $default_stmt->get_result();

    if ($default_result->num_rows > 0) {
        $default_row = $default_result->fetch_assoc();
        echo json_encode(['success' => true, 'fee' => $default_row['fee'], 'default' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Subject not found']);
    }

    $default_stmt->close();
}

$stmt->close();
$conn->close();
?>
