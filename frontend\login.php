
<?php

session_start();

// unauthorized access message
$unauthorized = isset($_GET['unauthorized']) ? true : false;
$error = isset($_SESSION['login_error']) ? $_SESSION['login_error'] : null;


if (isset($_SESSION['login_error'])) {
    unset($_SESSION['login_error']);
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Login - Gurukula Institution</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="../assets/css/responsive.css">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --light-bg: #f8f9fa;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    body {
      font-family: 'Poppins', sans-serif;
      background: linear-gradient(135deg, #4361ee, #3a0ca3);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem 1rem;
      position: relative;
      overflow: hidden;
    }

    .background-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      overflow: hidden;
    }

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 50%;
    }

    .shape-1 {
      width: 300px;
      height: 300px;
      top: -150px;
      left: -150px;
    }

    .shape-2 {
      width: 200px;
      height: 200px;
      bottom: -100px;
      right: -100px;
    }

    .shape-3 {
      width: 150px;
      height: 150px;
      bottom: 50%;
      left: 10%;
    }

    .shape-4 {
      width: 80px;
      height: 80px;
      top: 10%;
      right: 10%;
    }

    .shape-5 {
      width: 100px;
      height: 100px;
      top: 40%;
      right: 20%;
    }

    .login-container {
      width: 100%;
      max-width: 420px;
      padding: 0 15px;
      position: relative;
    }

    .login-card {
      background: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: var(--card-shadow);
      transition: all 0.3s ease;
      width: 100%;
    }

    .login-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 30px -10px rgba(0, 0, 0, 0.2);
    }

    .login-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: 2rem;
      text-align: center;
      position: relative;
    }

    .login-header::after {
      content: '';
      position: absolute;
      bottom: -20px;
      left: 0;
      right: 0;
      margin: 0 auto;
      width: 40px;
      height: 40px;
      background: white;
      transform: rotate(45deg);
      z-index: 1;
    }

    .login-header h3 {
      font-weight: 700;
      margin-bottom: 0.5rem;
      font-size: 1.75rem;
    }

    .login-header p {
      opacity: 0.8;
      margin-bottom: 0;
    }

    .login-body {
      padding: 2.5rem 2rem 2rem;
      position: relative;
      z-index: 2;
    }

    .form-floating {
      margin-bottom: 1.5rem;
    }

    .form-floating > .form-control {
      padding: 1.5rem 1rem;
      height: calc(3.5rem + 2px);
      border-radius: 12px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .form-floating > .form-control:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    }

    .form-floating > label {
      padding: 1rem;
    }

    .input-group-text {
      background-color: transparent;
      border-left: none;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .input-group-text:hover {
      color: var(--primary-color);
    }

    .form-check-input:checked {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }

    .btn-login {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border: none;
      border-radius: 12px;
      padding: 1rem;
      font-size: 1rem;
      font-weight: 600;
      width: 100%;
      margin-top: 1rem;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .btn-login::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: all 0.6s ease;
    }

    .btn-login:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 20px -5px rgba(67, 97, 238, 0.4);
    }

    .btn-login:hover::before {
      left: 100%;
    }

    .login-footer {
      text-align: center;
      margin-top: 1.5rem;
    }

    .login-footer a {
      color: white;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .login-footer a:hover {
      color: rgba(255, 255, 255, 0.8);
    }

    .alert {
      border-radius: 12px;
      padding: 1rem;
      margin-bottom: 1.5rem;
      border: none;
    }

    .alert-danger {
      background-color: rgba(231, 76, 60, 0.1);
      color: #e74c3c;
    }

    .brand-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 1rem;
      border-radius: 50%;
      padding: 0.5rem;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: auto;
      margin-right: auto;
    }

    .brand-logo i {
      font-size: 2.5rem;
    }

    /* Enhanced responsive styles */
    @media (max-width: 576px) {
      body {
        padding: 1rem 0.5rem;
      }

      .login-container {
        padding: 0 10px;
      }

      .login-header {
        padding: 1.5rem 1rem;
      }

      .login-header h3 {
        font-size: 1.5rem;
      }

      .login-body {
        padding: 2rem 1.25rem 1.5rem;
      }

      .form-floating > .form-control {
        height: calc(3.25rem + 2px);
        padding: 1.25rem 0.75rem;
      }

      .form-floating > label {
        padding: 0.75rem;
      }

      .d-flex.justify-content-between {
        flex-direction: column;
        gap: 0.75rem;
      }

      .d-flex.justify-content-between .form-check {
        margin-bottom: 0;
      }

      .btn-login {
        padding: 0.85rem;
      }

      .shape {
        opacity: 0.5;
      }
    }

    @media (max-width: 350px) {
      .login-header h3 {
        font-size: 1.25rem;
      }

      .login-header p {
        font-size: 0.9rem;
      }

      .brand-logo {
        width: 60px;
        height: 60px;
      }

      .brand-logo i {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div class="background-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
    <div class="shape shape-4"></div>
    <div class="shape shape-5"></div>
  </div>

  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="brand-logo">
          <i class="fas fa-graduation-cap"></i>
        </div>
        <h3>Welcome Back</h3>
        <p>Sign in to continue to Gurukula LMS</p>
      </div>

      <div class="login-body">
        <?php if(isset($error)) { echo "<div class='alert alert-danger'><i class='fas fa-exclamation-circle me-2'></i>$error</div>"; } ?>
        <?php if($unauthorized) { echo "<div class='alert alert-danger'><i class='fas fa-exclamation-circle me-2'></i>You are not authorized to access that page. Please log in with appropriate credentials.</div>"; } ?>

        <form method="POST" action="/gurukula_lms/backend/server/login.php">
          <div class="form-floating mb-4">
            <input type="email" name="email" id="email" class="form-control" placeholder="Email address" required>
            <label for="email"><i class="fas fa-envelope me-2"></i>Email address</label>
          </div>

          <div class="form-floating mb-3">
            <input type="password" name="password" id="password" class="form-control" placeholder="Password" required>
            <label for="password"><i class="fas fa-lock me-2"></i>Password</label>
          </div>

          <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="rememberMe">
              <label class="form-check-label" for="rememberMe">
                Remember me
              </label>
            </div>
            <a href="../backend/server/forgot_password.php" class="text-primary">Forgot password?</a>
          </div>

          <button type="submit" class="btn btn-login">
            <i class="fas fa-sign-in-alt me-2"></i>Sign In
          </button>
        </form>
      </div>
    </div>

    <div class="login-footer">
      <p class="text-white mt-3">
        <a href="../frontend/home.php"><i class="fas fa-home me-1"></i>Back to Home</a>
      </p>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/js/server_monitor.js"></script>
  <script>
    // Toggle password visibility
    document.addEventListener('DOMContentLoaded', function() {
      const passwordField = document.getElementById('password');

      // Add password toggle functionality
      const passwordToggle = document.createElement('span');
      passwordToggle.innerHTML = '<i class="fas fa-eye"></i>';
      passwordToggle.className = 'position-absolute top-50 end-0 translate-middle-y pe-3';
      passwordToggle.style.cursor = 'pointer';
      passwordToggle.style.zIndex = '5';

      passwordField.parentElement.style.position = 'relative';
      passwordField.parentElement.appendChild(passwordToggle);

      passwordToggle.addEventListener('click', function() {
        if (passwordField.type === 'password') {
          passwordField.type = 'text';
          passwordToggle.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
          passwordField.type = 'password';
          passwordToggle.innerHTML = '<i class="fas fa-eye"></i>';
        }
      });
    });
  </script>
</body>
</html>
