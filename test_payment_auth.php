<?php
// This is a test script to verify our payment page authentication works
echo "<h1>Payment Page Authentication Test</h1>";

// Try to access the parent payment page
$protected_page = "frontend/payments/parent_payment.php";
echo "<p>Trying to access: $protected_page</p>";

// Include the authentication file
require_once 'backend/server/auth.php';

// Check if the user has access to the page
$has_access = hasPageAccess($protected_page);

if ($has_access) {
    echo "<p style='color: green;'>Access granted! You can access the payment page.</p>";
} else {
    echo "<p style='color: red;'>Access denied! You would be redirected to the login page.</p>";
    echo "<p>Redirect URL: " . getLoginRedirectPath() . "</p>";
}

// Show current session information
echo "<h2>Current Session Information</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Show a link to test the payment page directly
echo "<p><a href='frontend/payments/parent_payment.php'>Try to access Payment Page</a></p>";

// Show a link to the login page
echo "<p><a href='frontend/login.php'>Go to Login Page</a></p>";
?>
