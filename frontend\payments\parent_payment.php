<?php

session_start();

// user --> parent
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'parent') {
    header("Location: ../../backend/server/login.php");
    exit();
}


include_once '../../backend/server/db_connect.php';


$is_logged_in = true;


$currentPage = basename($_SERVER['PHP_SELF']);
include_once '../../assets/parent_sidebar.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Student Fee Payment</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
    }
    .payment-container {
      max-width: 800px;
      margin: 50px auto;
    }
    /* Adjust container when sidebar is present */
    .content {
      margin-left: 260px;
      transition: margin-left 0.3s ease;
    }
    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding-top: 60px;
      }
    }
    @media (min-width: 769px) and (max-width: 992px) {
      .content {
        margin-left: 70px;
      }
    }
    .payment-card {
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .payment-header {
      background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
      color: white;
      padding: 20px;
    }
    .form-control:focus {
      border-color: #4b6cb7;
      box-shadow: 0 0 0 0.25rem rgba(75, 108, 183, 0.25);
    }
    .btn-primary {
      background: #4b6cb7;
      border-color: #4b6cb7;
    }
    .btn-primary:hover {
      background: #3a5a9f;
      border-color: #3a5a9f;
    }
    .test-card-info {
      background-color: #f8f9fa;
      border-left: 4px solid #4b6cb7;
      padding: 15px;
    }
    .card-number {
      font-family: monospace;
      letter-spacing: 1px;
    }
  </style>
</head>
<body>

<div class="content">

<div class="payment-container">
  <div class="payment-card">
    <div class="payment-header">
      <h2 class="text-center mb-0"><i class="fas fa-credit-card me-2"></i> Student Fee Payment</h2>
    </div>

    <div class="card-body p-4">
      <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        Please enter the student details and payment information below.
      </div>

      <form id="paymentForm" action="../../backend/payments/process_payment.php" method="POST">
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="studentId" class="form-label">Student ID <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="studentId" name="student_id" required
                   value="<?php echo isset($_GET['student_id']) ? htmlspecialchars($_GET['student_id']) : ''; ?>">
            <div id="studentInfo" class="mt-2"></div>
          </div>

          <div class="col-md-6">
            <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
            <select class="form-control" id="subject" name="subject_id" required>
              <option value="">Select Subject</option>
              <?php
              // Fetch subjects from database
              $query = "SELECT subject_id, subject_name FROM subjects ORDER BY subject_name";
              $result = $conn->query($query);
              while ($row = $result->fetch_assoc()) {
                $selected = (isset($_GET['subject_id']) && $_GET['subject_id'] == $row['subject_id']) ? 'selected' : '';
                echo "<option value='" . $row['subject_id'] . "' $selected>" . $row['subject_name'] . "</option>";
              }
              ?>
            </select>
          </div>

          <input type="hidden" id="grade" name="grade" value="">
         
        </div>

        <div class="row mb-3">
          <div class="col-md-6">
            <label for="paymentPeriod" class="form-label">Payment Period <span class="text-danger">*</span></label>
            <select class="form-control" id="paymentPeriod" name="payment_period" required>
              <?php
              
              $currentMonth = date('n');
              $currentYear = date('Y');

              // Show current month and next two months
              for ($i = 0; $i < 3; $i++) {
                $month = ($currentMonth + $i) > 12 ? ($currentMonth + $i - 12) : ($currentMonth + $i);
                $year = ($currentMonth + $i) > 12 ? ($currentYear + 1) : $currentYear;
                $monthName = date('F', mktime(0, 0, 0, $month, 1));
                $period = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT);
                echo "<option value='" . $period . "'>" . $monthName . " " . $year . "</option>";
              }
              ?>
            </select>
          </div>

          <div class="col-md-6">
            <label for="amount" class="form-label">Amount (LKR) <span class="text-danger">*</span></label>
            <input type="number" class="form-control" id="amount" name="amount" value="500" min="500" required>
            <small class="text-muted" id="fee-info">Minimum amount: 500 LKR</small>
          </div>
        </div>

        <div class="mb-4">
          <label for="notes" class="form-label">Additional Notes</label>
          <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
        </div>

        <div class="test-card-info mb-4">
          <h5><i class="fas fa-info-circle me-2"></i> Test Payment Information</h5>
          <p class="mb-2">This is a test payment system. Use the following test card to simulate a payment:</p>
          <p class="mb-1"><strong>Card Number:</strong> <span class="card-number">4242 4242 4242 4242</span></p>
          <p class="mb-1"><small>Use any future expiration date, any 3-digit CVC, and any postal code</small></p>
        </div>

        <div class="d-grid gap-2">
          <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-lock me-2"></i> Proceed to Secure Payment
          </button>
        </div>
      </form>
    </div>
  </div>

  <div class="text-center mt-3">
    <a href="../../frontend/dashboards/parent_dashboard.php" class="text-decoration-none">
      <i class="fas fa-arrow-left me-1"></i> Return to Dashboard
    </a>
  </div>
</div>

</div> 

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
  // Validate student ID
  $('#studentId').on('blur', function() {
    const studentId = $(this).val();
    if (studentId) {
      $.ajax({
        url: '../../backend/payments/validate_student.php',
        type: 'POST',
        data: { student_id: studentId },
        dataType: 'json',
        success: function(response) {
          if (response.exists) {
            // student's grade in the hidden field
            const grade = response.grade;
            const formattedGrade = grade === 'A/L' ? 'A/L' : `Grade ${grade}`;
            $('#grade').val(formattedGrade);

            $('#studentInfo').html(`
              <div class="alert alert-success py-2 mb-0">
                <small><strong>Student:</strong> ${response.name} <span class="badge bg-info ms-2">${formattedGrade}</span></small>
              </div>
            `);

            // If subject is already selected, fetch the fee
            if ($('#subject').val()) {
              getSubjectFee();
            }
          } else {
            $('#studentInfo').html(`
              <div class="alert alert-danger py-2 mb-0">
                <small>Student ID not found</small>
              </div>
            `);
            $('#grade').val('');
          }
        }
      });
    } else {
      $('#studentInfo').html('');
      $('#grade').val('');
    }
  });

  // Trigger validation ---> student ID is pre-filled
  if ($('#studentId').val()) {
    $('#studentId').trigger('blur');
  }

  //  get subject fee based on subject and grade
  function getSubjectFee() {
    const subjectId = $('#subject').val();
    const grade = $('#grade').val();
    const studentId = $('#studentId').val();

    if (subjectId && grade && studentId) {
      $.ajax({
        url: '../../backend/payments/get_subject_fee.php',
        type: 'POST',
        data: {
          subject_id: subjectId,
          grade: grade
        },
        dataType: 'json',
        success: function(response) {
          if (response.success) {
            // Update the amount field with the fee
            $('#amount').val(response.fee);

            if (response.default) {
              $('#fee-info').html(`<strong>Monthly fee: ${response.fee} LKR</strong> <span class="text-muted">(default fee)</span>`);
            } else {
              $('#fee-info').html(`<strong>Monthly fee: ${response.fee} LKR</strong> <span class="text-success">(${grade})</span>`);
            }
          } else {
            $('#amount').val(500);
            $('#fee-info').html('Minimum amount: 500 LKR');
          }
        },
        error: function() {
          $('#amount').val(500);
          $('#fee-info').html('Minimum amount: 500 LKR');
        }
      });
    } else {
      $('#amount').val(500);
      $('#fee-info').html('Minimum amount: 500 LKR');
    }
  }

  // Get subject fee when subject is changed
  $('#subject').on('change', function() {
    if ($('#studentId').val()) {
      getSubjectFee();
    }
  });
});
</script>
</body>
</html>
