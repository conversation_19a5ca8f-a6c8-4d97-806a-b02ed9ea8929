<?php
// Include database connection
include_once 'backend/server/db_connect.php';

// Check subjects table structure
echo "<h2>Subjects Table Structure</h2>";
$result = $conn->query("DESCRIBE subjects");
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check if fee column exists
$result = $conn->query("SHOW COLUMNS FROM subjects LIKE 'fee'");
if ($result->num_rows == 0) {
    echo "<h2>Adding 'fee' column to subjects table...</h2>";
    
    // Add the fee column
    if ($conn->query("ALTER TABLE subjects ADD COLUMN fee DECIMAL(10,2) DEFAULT 500.00 AFTER subject_name")) {
        echo "<p>Successfully added 'fee' column!</p>";
    } else {
        echo "<p>Error adding 'fee' column: " . $conn->error . "</p>";
    }
} else {
    echo "<h2>'fee' column already exists in subjects table.</h2>";
}

$conn->close();
?>
