<?php

include_once '../../backend/server/db_connect.php';


header('Content-Type: application/json');

$today = date('Y-m-d');

//get today's attendance count
$query = "SELECT COUNT(DISTINCT student_id) as total FROM attendance WHERE date = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $today);
$stmt->execute();
$result = $stmt->get_result();


$response = ['count' => 0];


if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $response['count'] = (int)$row['total'];
}

// Return JSON response
echo json_encode($response);


$stmt->close();
$conn->close();
