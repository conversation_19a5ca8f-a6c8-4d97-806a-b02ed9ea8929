<?php
session_start();
include_once '../../backend/server/db_connect.php';
include_once '../../assets/student_sidebar.php';


$user_id = $_SESSION['user_id'];


$stmt = $conn->prepare("SELECT student_id, grade FROM students WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();
$student_id = $student['student_id'];
$student_grade = $student['grade']; // Get the student's grade
$stmt->close();

// Get student's subjects
$stmt = $conn->prepare("
    SELECT ss.subject_id, s.subject_name
    FROM student_subjects ss
    JOIN subjects s ON ss.subject_id = s.subject_id
    WHERE ss.student_id = ?
");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$subjects_result = $stmt->get_result();
$subjects = [];
while ($row = $subjects_result->fetch_assoc()) {
    $subjects[] = $row;
}
$stmt->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <title>Student Forum</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--light-bg);
      color: var(--dark-text);
      margin: 0;
      padding: 0;
    }

    .content {
      margin-left: 260px;
      padding: 30px;
    }

    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }

    .question-form-card {
      background: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      border-top: 5px solid var(--primary-color);
    }

    .question-form-card h3 {
      color: var(--dark-text);
      font-weight: 600;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
    }

    .question-form-card h3 i {
      margin-right: 0.75rem;
      color: var(--primary-color);
    }

    .form-label {
      font-weight: 500;
      color: var(--dark-text);
      margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
      border-radius: 10px;
      padding: 0.75rem 1rem;
      border: 1px solid rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
      border-color: var(--primary-color);
    }

    textarea.form-control {
      min-height: 120px;
      resize: vertical;
    }

    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      border-radius: 10px;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
    }

    .btn-primary i {
      margin-right: 0.5rem;
    }

    .btn-primary:hover {
      background-color: var(--secondary-color);
      border-color: var(--secondary-color);
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .questions-section {
      margin-top: 3rem;
    }

    .questions-section h4 {
      color: var(--dark-text);
      font-weight: 600;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
    }

    .questions-section h4 i {
      margin-right: 0.75rem;
      color: var(--primary-color);
    }

    .question-card {
      background: white;
      border-radius: 16px;
      margin-bottom: 1.5rem;
      box-shadow: var(--card-shadow);
      transition: all 0.3s ease;
      overflow: hidden;
    }

    .question-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--hover-shadow);
    }

    .question-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.25rem 1.5rem;
      background-color: rgba(67, 97, 238, 0.05);
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .question-header .subject-badge {
      background-color: var(--primary-color);
      color: white;
      padding: 0.35rem 0.75rem;
      border-radius: 50px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .question-header .grade-badge {
      background-color: var(--secondary-color);
      color: white;
      padding: 0.35rem 0.75rem;
      border-radius: 50px;
      font-size: 0.8rem;
      font-weight: 600;
      margin-left: 0.5rem;
    }

    .question-header .date {
      color: var(--light-text);
      font-size: 0.85rem;
    }

    .question-body {
      padding: 1.5rem;
    }

    .question-body h6 {
      color: var(--dark-text);
      font-weight: 600;
      margin-bottom: 0.75rem;
    }

    .question-body p {
      color: var(--dark-text);
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .answer-card {
      background-color: rgba(67, 97, 238, 0.05);
      border-radius: 12px;
      padding: 1.25rem;
      margin-top: 1rem;
    }

    .answer-card h6 {
      color: var(--dark-text);
      font-weight: 600;
      margin-bottom: 0.75rem;
    }

    .answer-card p {
      color: var(--dark-text);
      line-height: 1.6;
      margin-bottom: 0.5rem;
    }

    .answer-card .answered-date {
      color: var(--light-text);
      font-size: 0.85rem;
      margin-top: 0.75rem;
    }

    .answer-card.unanswered {
      background-color: rgba(243, 156, 18, 0.05);
      border-left: 3px solid var(--warning-color);
    }

    .answer-card.answered {
      background-color: rgba(46, 204, 113, 0.05);
      border-left: 3px solid var(--success-color);
    }

    .spinner-border-sm {
      width: 1rem;
      height: 1rem;
      border-width: 0.15em;
    }

    .alert {
      border-radius: 12px;
      padding: 1rem 1.25rem;
      margin-bottom: 1.5rem;
    }

    .alert-success {
      background-color: rgba(46, 204, 113, 0.15);
      border-color: rgba(46, 204, 113, 0.3);
      color: var(--success-color);
    }

    /* Notification styles */
    .notification-container {
      position: relative;
    }

    #notificationBtn {
      background: white;
      border: none;
      width: 42px;
      height: 42px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }

    #notificationBtn:hover {
      background-color: #f8f9fa;
      transform: translateY(-2px);
    }

    #notificationBox {
      position: absolute;
      top: 100%;
      right: 0;
      width: 320px;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
      z-index: 1000;
      margin-top: 0.5rem;
      overflow: hidden;
      max-height: 400px;
      display: none;
    }

    #notificationBox .card-header {
      padding: 1rem 1.25rem;
      font-weight: 600;
    }

    .notification-item {
      padding: 1rem 1.25rem;
      border-bottom: 1px solid #edf2f7;
      transition: all 0.2s ease;
    }

    .notification-item:hover {
      background-color: #f8f9fa;
    }

    .notification-item .notification-title {
      font-weight: 500;
      margin-bottom: 0.25rem;
    }

    .notification-item .notification-time {
      font-size: 0.8rem;
      color: var(--light-text);
    }

    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }

      .page-header {
        padding: 1.5rem;
      }

      .question-form-card {
        padding: 1.5rem;
      }

      .question-header {
        flex-direction: column;
        align-items: flex-start;
      }

      .question-header .date {
        margin-top: 0.5rem;
      }

      #notificationBox {
        width: 280px;
        right: -10px;
      }
    }
  </style>
</head>
<body>

<div class="content">
  <!-- Notification Icon -->
  <div class="position-fixed top-0 end-0 p-3" style="z-index: 10000;">
    <div class="notification-container">
      <button class="btn position-relative" id="notificationBtn" onclick="toggleNotificationBox()">
        <i class="fas fa-bell"></i>
        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notificationCount">
          0
        </span>
      </button>

      <!-- Notification dropdown box -->
      <div id="notificationBox" class="card">
        <div class="card-header bg-primary text-white">
          <i class="fas fa-bell me-2"></i>Notifications
        </div>
        <ul class="list-group list-group-flush" id="notificationList"></ul>
      </div>
    </div>
  </div>

  <div class="page-header">
    <h2 class="mb-1"><i class="fas fa-comments me-2"></i>Student Forum</h2>
    <p class="mb-0">Ask questions and get answers from your teachers</p>
  </div>

  <div class="question-form-card">
    <h3><i class="fas fa-question-circle"></i>Ask a Question</h3>
    <form id="questionForm" class="mb-4">
      <div class="mb-3">
        <label for="subjectSelect" class="form-label">Subject</label>
        <select name="subject_id" id="subjectSelect" class="form-select" required>
          <option value="">Select a subject</option>
          <?php foreach ($subjects as $subject): ?>
            <option value="<?= $subject['subject_id'] ?>">
              <?= htmlspecialchars($subject['subject_name']) ?>
            </option>
          <?php endforeach; ?>
        </select>
      </div>
      <div class="mb-3">
        <label for="gradeField" class="form-label">Grade</label>
        <input type="text" name="grade" id="gradeField" class="form-control" value="<?= htmlspecialchars($student_grade) ?>" readonly>
      </div>
      <div class="mb-3">
        <label for="questionText" class="form-label">Your Question</label>
        <textarea name="question" id="questionText" class="form-control" placeholder="Type your question here..." required></textarea>
      </div>
      <button type="submit" class="btn btn-primary">
        <i class="fas fa-paper-plane"></i>Send Question
      </button>
    </form>
  </div>

  <div class="questions-section">
    <h4><i class="fas fa-history"></i>Your Questions</h4>
    <div id="questionList">
      
      <div class="d-flex justify-content-center">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    </div>
  </div>
</div>

<script>

let unreadCount = 0;

document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM fully loaded');
  loadQuestions();

  // Initialize notifications
  fetchNotifications();
  setInterval(fetchNotifications, 60000); // Check every minute
});

document.getElementById('questionForm').onsubmit = async function(e) {
  e.preventDefault();
  const formData = new FormData(this);

 
  const subjectId = document.getElementById('subjectSelect').value;
  const grade = document.getElementById('gradeField').value;
  const question = document.querySelector('textarea[name="question"]').value;

  // Check if any required field is empty
  if (!subjectId || !grade || !question) {
    alert('Please fill out all fields.');
    return;
  }

  try {
    // Show loading indicator
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';
    submitBtn.disabled = true;

    const res = await fetch('../../backend/forum/add_question.php', {
      method: 'POST',
      body: formData
    });

    const data = await res.json();

    if (data.success) {
      // Show success message
      const successAlert = document.createElement('div');
      successAlert.className = 'alert alert-success mt-3';
      successAlert.innerHTML = '<i class="fas fa-check-circle me-2"></i> Your question has been submitted successfully!';
      this.appendChild(successAlert);

      // Reset form but keep the grade
      document.getElementById('subjectSelect').value = '';
      document.getElementById('questionText').value = '';

      // Remove success message after 3 seconds
      setTimeout(() => {
        successAlert.remove();
      }, 3000);

      loadQuestions();
    } else {
      alert('Error submitting question: ' + (data.message || 'Unknown error'));
      console.error('Backend error:', data);
    }
  } catch (error) {
    console.error('Error submitting question:', error);
    alert('Error submitting question. Please try again later.');
  } finally {
    
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Question';
    submitBtn.disabled = false;
  }
};

function toggleNotificationBox() {
  const notificationBox = document.getElementById('notificationBox');
  if (notificationBox.style.display === 'none' || notificationBox.style.display === '') {
    notificationBox.style.display = 'block';
    // Mark notifications as read when opened
    if (unreadCount > 0) {
      fetch('../../backend/forum/mark_notifications_read.php')
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            document.getElementById('notificationCount').style.display = 'none';
            unreadCount = 0;
          }
        })
        .catch(error => console.error('Error marking notifications as read:', error));
    }
  } else {
    notificationBox.style.display = 'none';
  }
}

function fetchNotifications() {
  fetch('../../backend/forum/get_student_notifications.php')
    .then(res => res.json())
    .then(data => {
      console.log('Notifications data:', data);

      unreadCount = data.length;
      const notificationCount = document.getElementById('notificationCount');
      const notificationList = document.getElementById('notificationList');

      notificationCount.textContent = unreadCount;
      notificationCount.style.display = unreadCount > 0 ? 'inline-block' : 'none';
      notificationList.innerHTML = '';

      if (data.length === 0) {
        notificationList.innerHTML = `
          <li class="list-group-item text-center py-4">
            <i class="fas fa-bell-slash text-muted mb-2" style="font-size: 2rem;"></i>
            <p class="mb-0">No new notifications</p>
          </li>
        `;
      } else {
        data.forEach(notification => {
          const li = document.createElement('li');
          li.className = 'list-group-item notification-item';

          const timeAgo = getTimeAgo(new Date(notification.created_at));

          li.innerHTML = `
            <div class="notification-title">
              <i class="fas fa-comment-dots text-primary me-2"></i>
              ${notification.message}
            </div>
            <div class="notification-time">
              <i class="far fa-clock me-1"></i>${timeAgo}
            </div>
          `;

          notificationList.appendChild(li);
        });
      }
    })
    .catch(error => {
      console.error('Error fetching notifications:', error);
      
      const notificationList = document.getElementById('notificationList');
      notificationList.innerHTML = `
        <li class="list-group-item text-center py-4">
          <i class="fas fa-exclamation-circle text-danger mb-2" style="font-size: 2rem;"></i>
          <p class="mb-0">Error loading notifications</p>
          <small class="text-muted">Please try again later</small>
        </li>
      `;
    });
}

function getTimeAgo(date) {
  const seconds = Math.floor((new Date() - date) / 1000);

  let interval = Math.floor(seconds / 31536000);
  if (interval > 1) return interval + ' years ago';
  if (interval === 1) return '1 year ago';

  interval = Math.floor(seconds / 2592000);
  if (interval > 1) return interval + ' months ago';
  if (interval === 1) return '1 month ago';

  interval = Math.floor(seconds / 86400);
  if (interval > 1) return interval + ' days ago';
  if (interval === 1) return '1 day ago';

  interval = Math.floor(seconds / 3600);
  if (interval > 1) return interval + ' hours ago';
  if (interval === 1) return '1 hour ago';

  interval = Math.floor(seconds / 60);
  if (interval > 1) return interval + ' minutes ago';
  if (interval === 1) return '1 minute ago';

  return 'Just now';
}

async function loadQuestions() {
  try {
    // Show loading indicator
    document.getElementById('questionList').innerHTML = '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

    const res = await fetch('../../backend/forum/get_questions.php');
    const questions = await res.json();

    if (!Array.isArray(questions)) {
      document.getElementById('questionList').innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>Error loading questions</div>';
      return;
    }

    if (questions.length === 0) {
      document.getElementById('questionList').innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>You haven\'t asked any questions yet.</div>';
      return;
    }

    // Check for newly answered questions and highlight them
    const newlyAnswered = questions.filter(q => q.reply_text && q.is_new_reply === 1);
    if (newlyAnswered.length > 0) {
      console.log('Newly answered questions:', newlyAnswered.length);
    }

    let html = '';
    questions.forEach(q => {
      const date = new Date(q.created_at || Date.now()).toLocaleDateString();
      const repliedDate = q.replied_at ? new Date(q.replied_at).toLocaleDateString() : '';
      const isAnswered = q.reply_text ? true : false;
      const isNewReply = q.is_new_reply === 1;

      html += `
        <div class="question-card">
          <div class="question-header">
            <div>
              <span class="subject-badge">${q.subject_name || q.subject_id}</span>
              <span class="grade-badge">Grade ${q.grade}</span>
              ${isNewReply ? '<span class="badge bg-danger ms-2">NEW REPLY</span>' : ''}
            </div>
            <span class="date">Asked on: ${date}</span>
          </div>

          <div class="question-body">
            <h6>Question:</h6>
            <p>${q.question_text}</p>

            <div class="answer-card ${isAnswered ? (isNewReply ? 'answered border-danger' : 'answered') : 'unanswered'}">
              <h6>${isAnswered ? 'Answer:' : 'Status:'}</h6>
              ${isAnswered
                ? `<p>${isNewReply ? '<span class="badge bg-danger mb-2">New</span> ' : ''}${q.reply_text}</p>
                   <div class="answered-date">Answered on: ${repliedDate}</div>`
                : `<p><i class="fas fa-hourglass-half me-2"></i>Waiting for teacher's response...</p>`
              }
            </div>
          </div>
        </div>
      `;
    });

    document.getElementById('questionList').innerHTML = html;
  } catch (error) {
    console.error('Error loading questions:', error);
    document.getElementById('questionList').innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>Error loading questions. Please try again later.</div>';
  }
}
</script>
</body>
</html>
