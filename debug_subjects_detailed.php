<?php
session_start();
include_once 'backend/server/db_connect.php';

// Check if user is logged in as teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    echo "Please login as a teacher to view this page.";
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id
$stmt = $conn->prepare("SELECT teacher_id, first_name, last_name FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_id = null;
$teacher_name = "";
if ($row = $result->fetch_assoc()) {
    $teacher_id = $row['teacher_id'];
    $teacher_name = $row['first_name'] . ' ' . $row['last_name'];
}
$stmt->close();

echo "<h1>Detailed Subject Debug Information</h1>";
echo "<p>Teacher ID: $teacher_id</p>";
echo "<p>Teacher Name: $teacher_name</p>";

// Get all subjects from the database
$result = $conn->query("SELECT subject_id, subject_name FROM subjects ORDER BY subject_name");
$all_db_subjects = [];
while ($row = $result->fetch_assoc()) {
    $all_db_subjects[$row['subject_id']] = $row['subject_name'];
}

echo "<h2>All Subjects in Database</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
foreach ($all_db_subjects as $id => $name) {
    echo "<tr>";
    echo "<td>$id</td>";
    echo "<td>$name</td>";
    echo "</tr>";
}
echo "</table>";

// Get all subjects this teacher teaches
$stmt = $conn->prepare("
    SELECT ts.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_subject_rows = [];
while ($row = $result->fetch_assoc()) {
    $teacher_subject_rows[] = $row;
}
$stmt->close();

echo "<h2>All Teacher-Subject Relationships (Raw Data)</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
foreach ($teacher_subject_rows as $row) {
    echo "<tr>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Group by subject name
$grouped_by_name = [];
foreach ($teacher_subject_rows as $row) {
    $name = $row['subject_name'];
    if (!isset($grouped_by_name[$name])) {
        $grouped_by_name[$name] = [];
    }
    $grouped_by_name[$name][] = $row['subject_id'];
}

echo "<h2>Subjects Grouped by Name</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject Name</th><th>Subject IDs</th></tr>";
foreach ($grouped_by_name as $name => $ids) {
    echo "<tr>";
    echo "<td>$name</td>";
    echo "<td>" . implode(", ", $ids) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check for duplicate subject names with different IDs
$duplicate_names = [];
foreach ($grouped_by_name as $name => $ids) {
    if (count($ids) > 1) {
        $duplicate_names[$name] = $ids;
    }
}

if (!empty($duplicate_names)) {
    echo "<h2>Duplicate Subject Names with Different IDs</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Subject Name</th><th>Subject IDs</th></tr>";
    foreach ($duplicate_names as $name => $ids) {
        echo "<tr>";
        echo "<td>$name</td>";
        echo "<td>" . implode(", ", $ids) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p style='color:red;'><strong>Warning:</strong> The above subjects have the same name but different IDs. This can cause issues in the fee management system.</p>";
} else {
    echo "<h2>No Duplicate Subject Names Found</h2>";
    echo "<p style='color:green;'>All subject names are unique.</p>";
}

// Check for subject fees
echo "<h2>Subject Fees for This Teacher's Subjects</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th><th>Grade</th><th>Fee</th></tr>";

$subject_ids = array_column($teacher_subject_rows, 'subject_id');
$subject_ids_str = implode(',', $subject_ids);

if (!empty($subject_ids)) {
    $fees_query = "SELECT sf.subject_id, s.subject_name, sf.grade, sf.fee 
                  FROM subject_fees sf
                  JOIN subjects s ON sf.subject_id = s.subject_id
                  WHERE sf.subject_id IN ($subject_ids_str)
                  ORDER BY s.subject_name, sf.grade";
    
    $fees_result = $conn->query($fees_query);
    
    if ($fees_result->num_rows > 0) {
        while ($row = $fees_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['subject_id'] . "</td>";
            echo "<td>" . $row['subject_name'] . "</td>";
            echo "<td>" . $row['grade'] . "</td>";
            echo "<td>" . $row['fee'] . "</td>";
            echo "</tr>";
        }
    } else {
        echo "<tr><td colspan='4'>No fees found for this teacher's subjects</td></tr>";
    }
} else {
    echo "<tr><td colspan='4'>No subjects assigned to this teacher</td></tr>";
}
echo "</table>";

$conn->close();
?>
