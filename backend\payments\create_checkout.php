<?php
require 'config.php';

$student_id = $_POST['student_id'];
$amount = $_POST['amount']; 

// Create Stripe checkout session
$session = \Stripe\Checkout\Session::create([
    'payment_method_types' => ['card'],
    'line_items' => [[
        'price_data' => [
            'currency' => 'lkr',
            'product_data' => [
                'name' => 'Tuition Fee - Student ID: ' . $student_id,
            ],
            'unit_amount' => $amount * 100, 
        ],
        'quantity' => 1,
    ]],
    'mode' => 'payment',
    'success_url' => 'http://localhost/gurukula_lms/frontend/payments/payment_success.php',
    'cancel_url' => 'http://localhost/gurukula_lms/frontend/payments/payment_cancel.php',
]);


$conn->query("INSERT INTO payment_requests (student_id, amount, status, stripe_session_id)
              VALUES ('$student_id', '$amount', 'PENDING', '{$session->id}')");

// Redirect to Stripe checkout
header("Location: " . $session->url);
exit();
?>
