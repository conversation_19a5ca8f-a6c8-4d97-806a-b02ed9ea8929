<?php
// Fix the include path based on how the script is called
$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';
date_default_timezone_set("Asia/Colombo"); // Adjust as needed

// Add debugging
error_log("Running check_reminders.php at " . date('Y-m-d H:i:s'));

$now = new DateTime();
$nowFormatted = $now->format('Y-m-d H:i:00');
$future = (clone $now)->modify('+10 minutes')->format('H:i:00'); // Class in 10 mins
$today = $now->format('l'); 
$tomorrow = (clone $now)->modify('+1 day')->format('l'); // Tomorrow's day name

error_log("Today: $today, Tomorrow: $tomorrow, Current time: " . $now->format('H:i:00') . ", Future time: $future");

// Fetch upcoming classes in 10 mins
$stmt = $conn->prepare("
    SELECT tt.*, u.email, s.subject_name, t.email_notifications, t.popup_notifications
    FROM teacher_timetable tt
    JOIN teachers t ON tt.teacher_id = t.teacher_id
    JOIN users u ON t.user_id = u.id
    LEFT JOIN subjects s ON tt.subject_id = s.subject_id
    WHERE tt.day_of_week = ? AND tt.start_time = ?
");
$stmt->bind_param("ss", $today, $future);
$stmt->execute();
$result = $stmt->get_result();

error_log("Found " . $result->num_rows . " classes starting in 10 minutes");

while ($row = $result->fetch_assoc()) {
    $subject = $row['subject_name'] ?? 'your class';
    $message = "Reminder: You have a class for Grade {$row['grade']} ({$subject}) at {$row['start_time']} today.";

    // Insert into notifications table if enabled
    if ($row['popup_notifications'] == 1) {
        $insert = $conn->prepare("INSERT INTO notifications (teacher_id, message, scheduled_time) VALUES (?, ?, NOW())");
        $insert->bind_param("is", $row['teacher_id'], $message);
        $insert->execute();
    }

   
    if ($row['email_notifications'] == 1) {
        
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: Gurukula Institution <<EMAIL>>" . "\r\n";

        $emailBody = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                .header { background-color: #4a6fdc; color: white; padding: 10px; text-align: center; border-radius: 5px 5px 0 0; }
                .content { padding: 20px; }
                .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #666; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>Class Reminder</h2>
                </div>
                <div class='content'>
                    <p>Dear Teacher,</p>
                    <p><strong>{$message}</strong></p>
                    <p>Please ensure you are prepared for the class.</p>
                    <p>Thank you,<br>Gurukula Institution</p>
                </div>
                <div class='footer'>
                    This is an automated message. Please do not reply to this email.
                </div>
            </div>
        </body>
        </html>
        ";

        mail($row['email'], "Class Reminder - Grade {$row['grade']}", $emailBody, $headers);
    }
}

// Check for tomorrow's classes (day-before reminder)
$current_time = $now->format('H:i:00');
$stmt = $conn->prepare("
    SELECT tt.*, u.email, s.subject_name, t.email_notifications, t.popup_notifications
    FROM teacher_timetable tt
    JOIN teachers t ON tt.teacher_id = t.teacher_id
    JOIN users u ON t.user_id = u.id
    LEFT JOIN subjects s ON tt.subject_id = s.subject_id
    WHERE tt.day_of_week = ?
");
$stmt->bind_param("s", $tomorrow);
$stmt->execute();
$result = $stmt->get_result();

error_log("Found " . $result->num_rows . " classes for tomorrow ($tomorrow)");

while ($row = $result->fetch_assoc()) {
    $subject = $row['subject_name'] ?? 'your class';
    $message = "⚠️ IMPORTANT: You have a class for Grade {$row['grade']} ({$subject}) tomorrow at {$row['start_time']}.";

    error_log("Processing tomorrow reminder for teacher ID: " . $row['teacher_id'] . ", Subject: $subject, Grade: " . $row['grade']);

    // Insert into notifications table (for popup) if enabled
    if ($row['popup_notifications'] == 1) {
        error_log("Popup notifications enabled for this teacher");

        // Check if we already sent this notification today
        $check = $conn->prepare("
            SELECT id FROM notifications
            WHERE teacher_id = ?
            AND message LIKE ?
            AND DATE(scheduled_time) = CURDATE()
        ");
        $like_param = "%tomorrow at {$row['start_time']}%";
        $check->bind_param("is", $row['teacher_id'], $like_param);
        $check->execute();
        $check_result = $check->get_result();

        error_log("Already sent today: " . ($check_result->num_rows > 0 ? 'Yes' : 'No'));

        // Only send if we haven't already sent today
        if ($check_result->num_rows == 0) {
            
            $insert = $conn->prepare("
                INSERT INTO notifications
                (teacher_id, message, scheduled_time, is_read)
                VALUES (?, ?, NOW(), 0)
            ");
            $insert->bind_param("is", $row['teacher_id'], $message);
            $result = $insert->execute();
            error_log("Notification inserted: " . ($result ? 'Success' : 'Failed - ' . $insert->error));
        }
    } else {
        error_log("Popup notifications disabled for this teacher");
    }

    // Send email if enabled (only once per day)
    if ($row['email_notifications'] == 1) {
        // Check if we already sent this email today
        $check = $conn->prepare("
            SELECT id FROM email_logs
            WHERE teacher_id = ?
            AND subject LIKE ?
            AND DATE(sent_time) = CURDATE()
        ");
        $like_param = "%tomorrow at {$row['start_time']}%";
        $check->bind_param("is", $row['teacher_id'], $like_param);
        $check->execute();
        $check_result = $check->get_result();

        // Only send if we haven't already sent today
        if ($check_result->num_rows == 0) {
            // Set email headers
            $headers = "MIME-Version: 1.0" . "\r\n";
            $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
            $headers .= "From: Gurukula Institution <<EMAIL>>" . "\r\n";

            $emailBody = "
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                    .header { background-color: #4a6fdc; color: white; padding: 10px; text-align: center; border-radius: 5px 5px 0 0; }
                    .content { padding: 20px; }
                    .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #666; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h2>Class Reminder</h2>
                    </div>
                    <div class='content'>
                        <p>Dear Teacher,</p>
                        <p><strong>{$message}</strong></p>
                        <p>Please ensure you are prepared for the class.</p>
                        <p>Thank you,<br>Gurukula Institution</p>
                    </div>
                    <div class='footer'>
                        This is an automated message. Please do not reply to this email.
                    </div>
                </div>
            </body>
            </html>
            ";

            $email_subject = "Class Reminder - Grade {$row['grade']} Tomorrow";
            mail($row['email'], $email_subject, $emailBody, $headers);

            // Log the email to avoid duplicates
            $log = $conn->prepare("INSERT INTO email_logs (teacher_id, subject, sent_time) VALUES (?, ?, NOW())");
            $log->bind_param("is", $row['teacher_id'], $email_subject);
            $log->execute();
        }
    }
}

$stmt->close();

// If this is an AJAX request, return JSON data
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'message' => 'Reminders checked and sent.']);
} else {
    
    $redirect_url = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '../../frontend/timetables/teacher_timetable.php';
    header("Location: $redirect_url?reminder_check=success");
}

$conn->close();
?>
