<?php
session_start();
include_once '../../backend/server/db_connect.php';
header('Content-Type: application/json');


ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'worker') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}


error_log("Received manual attendance data: " . json_encode($_POST));


if (!isset($_POST['student_id']) || !isset($_POST['date'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
    exit();
}

$student_id = intval($_POST['student_id']);
$date = $_POST['date'];
$week = $_POST['week'] ?? '';

// Validate date format
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
    echo json_encode(['success' => false, 'message' => 'Invalid date format']);
    exit();
}


$stmt = $conn->prepare("
    SELECT s.student_id, CONCAT(u.first_name, ' ', u.last_name) AS student_name,
           s.grade, ss.subject_id, sub.subject_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN student_subjects ss ON s.student_id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.subject_id
    WHERE s.student_id = ?
    LIMIT 1
");

if (!$stmt) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $conn->error]);
    exit();
}

$stmt->bind_param("i", $student_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Student not found']);
    exit();
}

$student = $result->fetch_assoc();
$student_name = $student['student_name'];
$grade = $student['grade'];
$subject_id = $student['subject_id'] ?? null;
$subject_name = $student['subject_name'] ?? 'General';

// Check if attendance already marked for this date
$check_stmt = $conn->prepare("SELECT * FROM attendance WHERE student_id = ? AND date = ?");
$check_stmt->bind_param("is", $student_id, $date);
$check_stmt->execute();
$check_result = $check_stmt->get_result();

if ($check_result->num_rows > 0) {
    
    $update_stmt = $conn->prepare("
        UPDATE attendance
        SET time = NOW(),
            marked_by = ?,
            week = ?,
            grade = ?,
            subject_id = ?,
            subject_name = ?
        WHERE student_id = ? AND date = ?
    ");

    $update_stmt->bind_param("sssisis",
        $_SESSION['user_id'],
        $week,
        $grade,
        $subject_id,
        $subject_name,
        $student_id,
        $date
    );

    if ($update_stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'Attendance record updated successfully',
            'student_id' => $student_id,
            'student_name' => $student_name,
            'date' => $date,
            'week' => $week,
            'updated' => true
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Database error while updating: ' . $update_stmt->error
        ]);
    }

    $update_stmt->close();
    exit();
}


$insert_stmt = $conn->prepare("
    INSERT INTO attendance
    (student_id, student_name, date, time, marked_by, week, grade, subject_id, subject_name)
    VALUES (?, ?, ?, NOW(), ?, ?, ?, ?, ?)
");

if (!$insert_stmt) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $conn->error]);
    exit();
}

try {
    $insert_stmt->bind_param("isssisis",
        $student_id,
        $student_name,
        $date,
        $_SESSION['user_id'],
        $week,
        $grade,
        $subject_id,
        $subject_name
    );
} catch (Exception $e) {
   
    error_log("Binding error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database binding error: ' . $e->getMessage()]);
    exit();
}

if ($insert_stmt->execute()) {
    echo json_encode([
        'success' => true,
        'message' => 'Attendance marked successfully',
        'student_id' => $student_id,
        'student_name' => $student_name,
        'date' => $date,
        'week' => $week
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $insert_stmt->error
    ]);
}

$insert_stmt->close();
$conn->close();
?>
