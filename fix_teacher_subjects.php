<?php
include_once 'backend/server/db_connect.php';

echo "<h1>Fix Teacher Subjects Data</h1>";

// Get all teacher_subjects entries
$result = $conn->query("SELECT * FROM teacher_subjects ORDER BY teacher_id, subject_id");

echo "<h2>Current teacher_subjects Data</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Teacher ID</th><th>Subject ID</th></tr>";
$teacher_subjects = [];
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['teacher_id'] . "</td>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "</tr>";
    
    // Store for later processing
    $teacher_subjects[] = $row;
}
echo "</table>";

// Get all subjects
$result = $conn->query("SELECT subject_id, subject_name FROM subjects ORDER BY subject_id");
$subjects = [];
while ($row = $result->fetch_assoc()) {
    $subjects[$row['subject_id']] = $row['subject_name'];
}

echo "<h2>Subjects in Database</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
foreach ($subjects as $id => $name) {
    echo "<tr>";
    echo "<td>" . $id . "</td>";
    echo "<td>" . $name . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check for duplicate entries (same teacher_id and subject_id)
echo "<h2>Checking for Duplicate Entries</h2>";
$duplicates = [];
$unique_combinations = [];

foreach ($teacher_subjects as $entry) {
    $key = $entry['teacher_id'] . '-' . $entry['subject_id'];
    if (isset($unique_combinations[$key])) {
        $duplicates[] = $entry;
    } else {
        $unique_combinations[$key] = $entry;
    }
}

if (count($duplicates) > 0) {
    echo "<p>Found " . count($duplicates) . " duplicate entries!</p>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Teacher ID</th><th>Subject ID</th><th>Subject Name</th><th>Action</th></tr>";
    foreach ($duplicates as $dup) {
        echo "<tr>";
        echo "<td>" . $dup['id'] . "</td>";
        echo "<td>" . $dup['teacher_id'] . "</td>";
        echo "<td>" . $dup['subject_id'] . "</td>";
        echo "<td>" . $subjects[$dup['subject_id']] . "</td>";
        echo "<td><a href='?delete=" . $dup['id'] . "'>Delete</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No duplicate entries found.</p>";
}

// Handle deletion of duplicate entries
if (isset($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    $stmt = $conn->prepare("DELETE FROM teacher_subjects WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        echo "<p>Successfully deleted entry with ID: " . $id . "</p>";
        echo "<p><a href='fix_teacher_subjects.php'>Refresh</a></p>";
    } else {
        echo "<p>Error deleting entry: " . $stmt->error . "</p>";
    }
    $stmt->close();
}

// Check for teachers with multiple subjects
echo "<h2>Teachers with Multiple Subjects</h2>";
$teacher_subject_map = [];
foreach ($teacher_subjects as $entry) {
    if (!isset($teacher_subject_map[$entry['teacher_id']])) {
        $teacher_subject_map[$entry['teacher_id']] = [];
    }
    $teacher_subject_map[$entry['teacher_id']][] = $entry['subject_id'];
}

echo "<table border='1'>";
echo "<tr><th>Teacher ID</th><th>Subject IDs</th><th>Subject Names</th></tr>";
foreach ($teacher_subject_map as $teacher_id => $subject_ids) {
    echo "<tr>";
    echo "<td>" . $teacher_id . "</td>";
    echo "<td>" . implode(", ", $subject_ids) . "</td>";
    
    $subject_names = [];
    foreach ($subject_ids as $subject_id) {
        $subject_names[] = $subjects[$subject_id];
    }
    echo "<td>" . implode(", ", $subject_names) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Add a link to go back to manage_fees.php
echo "<p><a href='frontend/teacher/manage_fees.php'>Go to Manage Fees Page</a></p>";

$conn->close();
?>
