<?php
include_once '../../backend/server/db_connect.php';

// Check if notifications table exists
$tableExists = false;
$result = $conn->query("SHOW TABLES LIKE 'notifications'");
if ($result->num_rows > 0) {
    $tableExists = true;
    echo "Notifications table already exists.<br>";
}

// Create notifications table if it doesn't exist
if (!$tableExists) {
    $sql = "CREATE TABLE notifications (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        teacher_id INT(11) NOT NULL,
        message VARCHAR(255) NOT NULL,
        is_read TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        scheduled_time TIMESTAMP NULL
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Notifications table created successfully.<br>";
    } else {
        echo "Error creating notifications table: " . $conn->error . "<br>";
        exit;
    }
}

// Check if there are any questions in the database
$result = $conn->query("SELECT COUNT(*) as count FROM questions");
$row = $result->fetch_assoc();
$questionCount = $row['count'];

echo "Found $questionCount questions in the database.<br>";

// Get all teachers
$teacherResult = $conn->query("SELECT teacher_id FROM teachers");
if ($teacherResult->num_rows > 0) {
    echo "Found " . $teacherResult->num_rows . " teachers.<br>";
    
    // Get all unanswered questions
    $questionResult = $conn->query("
        SELECT q.id, q.student_id, q.subject_id, q.question_text, q.created_at, 
               s.subject_name, ts.teacher_id
        FROM questions q
        JOIN subjects s ON q.subject_id = s.subject_id
        JOIN teacher_subjects ts ON s.subject_id = ts.subject_id
        WHERE q.reply_text IS NULL OR q.reply_text = ''
    ");
    
    if ($questionResult->num_rows > 0) {
        echo "Found " . $questionResult->num_rows . " unanswered questions.<br>";
        
        // Clear existing notifications
        $conn->query("DELETE FROM notifications");
        echo "Cleared existing notifications.<br>";
        
        // Create notifications for each unanswered question
        $insertCount = 0;
        while ($question = $questionResult->fetch_assoc()) {
            $teacherId = $question['teacher_id'];
            $subjectName = $question['subject_name'];
            $message = "New question in $subjectName needs your attention";
            
            $stmt = $conn->prepare("
                INSERT INTO notifications (teacher_id, message, created_at) 
                VALUES (?, ?, ?)
            ");
            $stmt->bind_param("iss", $teacherId, $message, $question['created_at']);
            
            if ($stmt->execute()) {
                $insertCount++;
            }
        }
        
        echo "Created $insertCount new notifications.<br>";
    } else {
        echo "No unanswered questions found.<br>";
    }
} else {
    echo "No teachers found in the database.<br>";
}

echo "Notification setup complete.";
?>
