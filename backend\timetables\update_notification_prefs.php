<?php
session_start();
// Fix the include path based on how the script is called
$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';

// Check if user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Not authorized']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Get teacher_id from user_id
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Teacher record not found']);
    exit;
}

$teacher = $result->fetch_assoc();
$teacher_id = $teacher['teacher_id'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Set default values to 0 if checkboxes are not checked
    $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
    $popup_notifications = isset($_POST['popup_notifications']) ? 1 : 0;

    // Update the teacher's notification preferences
    $sql = "UPDATE teachers SET email_notifications = ?, popup_notifications = ? WHERE teacher_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iii", $email_notifications, $popup_notifications, $teacher_id);

    if ($stmt->execute()) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true, 
            'message' => 'Notification preferences saved successfully',
            'preferences' => [
                'email_notifications' => $email_notifications,
                'popup_notifications' => $popup_notifications
            ]
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Failed to save preferences: ' . $conn->error]);
    }
} else {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
