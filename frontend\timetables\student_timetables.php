<?php include_once '../../assets/student_sidebar.php'; ?>
<!DOCTYPE html>
<html lang="en">
<head>
  <title>My Timetables</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--light-bg);
      color: var(--dark-text);
    }

    .content {
      margin-left: 260px;
      padding: 30px;
    }

    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }

    .filters-card {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      border-top: 5px solid var(--primary-color);
    }

    .filters-card h5 {
      color: var(--dark-text);
      font-weight: 600;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
    }

    .filters-card h5 i {
      margin-right: 0.75rem;
      color: var(--primary-color);
    }

    .form-label {
      font-weight: 500;
      color: var(--dark-text);
      margin-bottom: 0.5rem;
    }

    .form-select {
      border-radius: 10px;
      padding: 0.75rem 1rem;
      border: 1px solid rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .form-select:focus {
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
      border-color: var(--primary-color);
    }

    .btn-danger {
      background-color: var(--danger-color);
      border-color: var(--danger-color);
      border-radius: 10px;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
    }

    .btn-danger i {
      margin-right: 0.5rem;
    }

    .btn-danger:hover {
      background-color: #c0392b;
      border-color: #c0392b;
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .table-card {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }

    .table-responsive {
      overflow-x: auto;
    }

    table.dataTable {
      border-collapse: collapse !important;
      width: 100% !important;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
      color: var(--dark-text);
      margin-bottom: 1rem;
    }

    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      padding: 0.5rem;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
      border-radius: 50%;
      width: 36px;
      height: 36px;
      padding: 0;
      line-height: 36px;
      text-align: center;
      margin: 0 3px;
      border: none !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
      background: var(--primary-color) !important;
      color: white !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
      background: rgba(67, 97, 238, 0.1) !important;
      color: var(--primary-color) !important;
    }

    table.dataTable thead th {
      background-color: rgba(67, 97, 238, 0.05);
      color: var(--dark-text);
      font-weight: 600;
      border-bottom: none;
      padding: 1rem;
    }

    table.dataTable tbody td {
      padding: 1rem;
      vertical-align: middle;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    table.dataTable tbody tr:last-child td {
      border-bottom: none;
    }

    table.dataTable tbody tr:hover {
      background-color: rgba(67, 97, 238, 0.03);
    }

    .day-badge {
      background-color: var(--primary-color);
      color: white;
      padding: 0.35rem 0.75rem;
      border-radius: 50px;
      font-size: 0.8rem;
      font-weight: 600;
      display: inline-block;
    }

    .grade-badge {
      background-color: var(--secondary-color);
      color: white;
      padding: 0.35rem 0.75rem;
      border-radius: 50px;
      font-size: 0.8rem;
      font-weight: 600;
      display: inline-block;
    }

    .subject-badge {
      background-color: var(--accent-color);
      color: white;
      padding: 0.35rem 0.75rem;
      border-radius: 50px;
      font-size: 0.8rem;
      font-weight: 600;
      display: inline-block;
    }

    .teacher-badge {
      background-color: var(--success-color);
      color: white;
      padding: 0.35rem 0.75rem;
      border-radius: 50px;
      font-size: 0.8rem;
      font-weight: 600;
      display: inline-block;
    }

    .time-badge {
      background-color: var(--warning-color);
      color: white;
      padding: 0.35rem 0.75rem;
      border-radius: 50px;
      font-size: 0.8rem;
      font-weight: 600;
      display: inline-block;
    }

    .spinner-border {
      width: 3rem;
      height: 3rem;
    }

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 3rem;
    }

    .error-message {
      text-align: center;
      padding: 2rem;
      color: var(--danger-color);
      background-color: rgba(231, 76, 60, 0.1);
      border-radius: 10px;
      margin: 2rem 0;
    }

    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }

      .page-header {
        padding: 1.5rem;
      }

      .filters-card {
        padding: 1.5rem;
      }

      .table-card {
        padding: 1rem;
      }

      .filters .col-md-3 {
        margin-bottom: 1rem;
      }
    }
  </style>
</head>
<body>
<div class="content">
  <div class="page-header">
    <h2 class="mb-1"><i class="fas fa-calendar-alt me-2"></i>My Timetables</h2>
    <p class="mb-0">View and filter your class schedule</p>
    <div class="mt-3">
      <button id="checkNotificationsBtn" class="btn btn-warning btn-sm">
        <i class="fas fa-check-circle me-2"></i>Check for Reminders Now
      </button>
      <a href="../settings/student_settings.php?tab=notifications" class="btn btn-outline-light btn-sm ms-2">
        <i class="fas fa-cog me-2"></i>Notification Settings
      </a>
    </div>
  </div>

  <div class="filters-card">
    <h5><i class="fas fa-filter"></i>Filter Options</h5>
    <div class="row filters align-items-end">
      <div class="col-md-3">
        <label class="form-label">Subject</label>
        <select id="subjectFilter" class="form-select">
          <option value="">All Subjects</option>
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">Grade</label>
        <select id="gradeFilter" class="form-select">
          <option value="">All Grades</option>
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">Teacher</label>
        <select id="teacherFilter" class="form-select">
          <option value="">All Teachers</option>
        </select>
      </div>
      <div class="col-md-3 text-end">
        <button id="clearFiltersBtn" class="btn btn-danger">
          <i class="fas fa-times"></i>Clear Filters
        </button>
      </div>
    </div>
  </div>

  <div class="table-card">
    <div id="loadingSpinner" class="loading-container">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <div id="errorMessage" class="error-message" style="display: none;">
      <i class="fas fa-exclamation-circle fa-2x mb-3"></i>
      <h5>Failed to load timetables</h5>
      <p>Please try refreshing the page or contact support if the problem persists.</p>
    </div>

    <div class="table-responsive" style="display: none;">
      <table id="studentTimetableTable" class="table table-hover">
        <thead>
          <tr>
            <th>Day</th>
            <th>Grade</th>
            <th>Subject</th>
            <th>Teacher</th>
            <th>Time</th>
          </tr>
        </thead>
        <tbody>
         
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>

<script>
  let table;

  async function loadStudentTimetables() {
    try {
      // Show loading indicator
      document.getElementById('loadingSpinner').style.display = 'flex';
      document.querySelector('.table-responsive').style.display = 'none';
      document.getElementById('errorMessage').style.display = 'none';

      // Add debugging to see what's happening
      console.log('Fetching student timetables...');

      const res = await fetch('../../backend/timetables/get_student_timetables.php');
      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`);
      }

      const data = await res.json();
      console.log('Timetables loaded:', data);

      if (!Array.isArray(data) || data.length === 0) {
        document.getElementById('loadingSpinner').style.display = 'none';
        document.getElementById('errorMessage').style.display = 'block';
        document.getElementById('errorMessage').innerHTML = `
          <i class="fas fa-info-circle fa-2x mb-3"></i>
          <h5>No timetables found</h5>
          <p>You don't have any classes scheduled yet.</p>
        `;
        return;
      }

      let rows = '';
      const subjects = new Set();
      const grades = new Set();
      const teachers = new Set();

      data.forEach(t => {
        rows += `<tr>
                   <td><span class="day-badge">${t.day}</span></td>
                   <td><span class="grade-badge">${t.grade}</span></td>
                   <td><span class="subject-badge">${t.subject}</span></td>
                   <td><span class="teacher-badge">${t.teacher_name}</span></td>
                   <td><span class="time-badge">${t.time}</span></td>
                 </tr>`;

        subjects.add(t.subject);
        grades.add(t.grade);
        teachers.add(t.teacher_name);
      });

      document.querySelector('#studentTimetableTable tbody').innerHTML = rows;

      // Hide loading indicator and show table
      document.getElementById('loadingSpinner').style.display = 'none';
      document.querySelector('.table-responsive').style.display = 'block';

      // Initialize DataTable
      if ($.fn.DataTable.isDataTable('#studentTimetableTable')) {
        table.destroy();
      }

      table = $('#studentTimetableTable').DataTable({
        "order": [[0, 'asc'], [4, 'asc']],
        "pageLength": 10,
        "language": {
          "emptyTable": "No timetables found",
          "zeroRecords": "No matching records found",
          "info": "Showing _START_ to _END_ of _TOTAL_ entries",
          "infoEmpty": "Showing 0 to 0 of 0 entries",
          "infoFiltered": "(filtered from _MAX_ total entries)",
          "search": "<i class='fas fa-search'></i> Search:",
          "paginate": {
            "first": "<i class='fas fa-angle-double-left'></i>",
            "last": "<i class='fas fa-angle-double-right'></i>",
            "next": "<i class='fas fa-angle-right'></i>",
            "previous": "<i class='fas fa-angle-left'></i>"
          }
        },
        "responsive": true
      });

      // Populate filter dropdowns
      populateFilterDropdowns(subjects, grades, teachers);

    } catch (error) {
      console.error('Error loading timetables:', error);
      document.getElementById('loadingSpinner').style.display = 'none';
      document.getElementById('errorMessage').style.display = 'block';
      document.querySelector('.table-responsive').style.display = 'none';
    }
  }

  function populateFilterDropdowns(subjects, grades, teachers) {
    // Clear existing options except the first one
    $('#subjectFilter').find('option:not(:first)').remove();
    $('#gradeFilter').find('option:not(:first)').remove();
    $('#teacherFilter').find('option:not(:first)').remove();

    // Add new options
    subjects.forEach(subject => {
      $('#subjectFilter').append(`<option value="${subject}">${subject}</option>`);
    });

    grades.forEach(grade => {
      $('#gradeFilter').append(`<option value="${grade}">${grade}</option>`);
    });

    teachers.forEach(teacher => {
      $('#teacherFilter').append(`<option value="${teacher}">${teacher}</option>`);
    });
  }

  function applyFilters() {
    const subject = $('#subjectFilter').val();
    const grade = $('#gradeFilter').val();
    const teacher = $('#teacherFilter').val();

    table.column(2).search(subject, true, false)
         .column(1).search(grade, true, false)
         .column(3).search(teacher, true, false)
         .draw();
  }

  function clearFilters() {
    $('#subjectFilter').val('');
    $('#gradeFilter').val('');
    $('#teacherFilter').val('');

    table.columns().search('').draw();
  }

  // Function to request notification permission
  function requestNotificationPermission() {
    if (!("Notification" in window)) {
      alert("This browser does not support desktop notifications");
      return false;
    }

    if (Notification.permission === "granted") {
      return true;
    } else if (Notification.permission !== "denied") {
      Notification.requestPermission().then(function (permission) {
        return permission === "granted";
      });
    }

    return Notification.permission === "granted";
  }

  // Function to show browser notification
  function showBrowserNotification(title, message) {
    if (Notification.permission === "granted") {
      const notification = new Notification(title, {
        body: message,
        icon: '../../assets/images/logo.png'
      });

      notification.onclick = function() {
        window.focus();
        this.close();
      };

      // Close the notification after 10 seconds
      setTimeout(() => {
        notification.close();
      }, 10000);
    }
  }

  // Function to show toast notification
  function showToastNotification(message) {
    $('#notificationMessage').html(message);
    const toast = new bootstrap.Toast(document.getElementById('notificationToast'));
    toast.show();
  }



  // Function to check for upcoming class reminders
  async function checkForReminders() {
    try {
      const response = await fetch('../../backend/timetables/check_class_reminders.php');
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Reminders check response:', data);

      if (data.success) {
        if (data.reminders && data.reminders.length > 0) {
          // Display reminders
          let reminderMessage = '<strong>Upcoming Classes:</strong><ul class="mb-0 ps-3">';

          data.reminders.forEach(reminder => {
            reminderMessage += `<li><strong>${reminder.subject}</strong> - ${reminder.day} at ${reminder.time}</li>`;
          });

          reminderMessage += '</ul>';

          // Show toast notification
          showToastNotification(reminderMessage);

          // Show browser notification if enabled
          if (data.preferences && data.preferences.browser_notifications === "1" && Notification.permission === "granted") {
            showBrowserNotification(
              'Upcoming Classes Reminder',
              `You have ${data.reminders.length} upcoming class(es). Click to view details.`
            );
          }
        } else {
          showToastNotification('<i class="fas fa-info-circle text-info me-2"></i> No upcoming classes found for the reminder period.');
        }
      } else {
        showToastNotification('<i class="fas fa-exclamation-circle text-warning me-2"></i> ' + (data.message || 'Failed to check for reminders.'));
      }
    } catch (error) {
      console.error('Error checking for reminders:', error);
      showToastNotification('<i class="fas fa-exclamation-circle text-danger me-2"></i> An error occurred while checking for reminders.');
    }
  }

  $(document).ready(function() {
    loadStudentTimetables();

    // Filter event handlers
    $('#subjectFilter, #gradeFilter, #teacherFilter').on('change', function() {
      applyFilters();
    });

    $('#clearFiltersBtn').on('click', function() {
      clearFilters();
    });

    $('#checkNotificationsBtn').on('click', function() {
      checkForReminders();
    });

    // Check for browser notification permission
    if (Notification.permission === "granted") {
      console.log("Browser notifications are allowed");
    } else if (Notification.permission === "denied") {
      console.log("Browser notifications are blocked");
    }

    // Automatically check for reminders when the page loads
    setTimeout(checkForReminders, 2000);
  });
</script>



<!-- Notification Toast -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
  <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header bg-primary text-white">
      <i class="fas fa-bell me-2"></i>
      <strong class="me-auto">Class Reminder</strong>
      <small>Just now</small>
      <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      <div id="notificationMessage"></div>
    </div>
  </div>
</div>

</body>
</html>
