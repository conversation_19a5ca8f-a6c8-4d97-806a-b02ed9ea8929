<?php
include_once '../../backend/server/db_connect.php';
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../frontend/login.php");
    exit();
}

// Get teacher ID
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$teacher_id = $teacher['teacher_id'];
$stmt->close();

// Set timezone
date_default_timezone_set("Asia/Colombo"); // Adjust as needed

// Get current date and time
$now = new DateTime();
$today = $now->format('l'); // E.g., "Monday"
$tomorrow = (clone $now)->modify('+1 day')->format('l'); // Tomorrow's day name

// Check if we should create a test notification
$create_test = isset($_GET['create']) && $_GET['create'] == 'true';

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Class Reminder</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
            --light-bg: #f8fafc;
            --dark-text: #2d3748;
            --light-text: #718096;
            --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            background-color: var(--light-bg);
            font-family: "Poppins", sans-serif;
            color: var(--dark-text);
            padding: 0;
            margin: 0;
        }

        .content-wrapper {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
            position: relative;
        }

        .page-title {
            font-weight: 700;
            color: var(--dark-text);
            margin-bottom: 0.5rem;
            font-size: 2.25rem;
        }

        .page-subtitle {
            color: var(--light-text);
            font-weight: 400;
        }

        .card {
            border: none;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }

        .card:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-5px);
        }

        .card-header {
            border-bottom: none;
            padding: 1.5rem;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .card-header.primary-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .card-header.info-header {
            background: linear-gradient(135deg, var(--accent-color), var(--info-color));
            color: white;
        }

        .card-header::before {
            content: "";
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(30deg);
        }

        .card-body {
            padding: 1.75rem;
        }

        .list-group {
            border-radius: 10px;
            overflow: hidden;
        }

        .list-group-item {
            padding: 1rem 1.25rem;
            border-left: none;
            border-right: none;
            transition: all 0.2s ease;
            background-color: transparent;
        }

        .list-group-item:hover {
            background-color: #f1f5f9;
            transform: translateX(5px);
        }

        .class-item {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .class-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background-color: rgba(67, 97, 238, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 1.25rem;
        }

        .class-details {
            flex: 1;
        }

        .class-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .class-time {
            color: var(--light-text);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn {
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #64748b;
            border: none;
        }

        .btn-secondary:hover {
            background: #475569;
            transform: translateY(-2px);
        }

        .alert {
            border-radius: 10px;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-success {
            background-color: rgba(46, 204, 113, 0.15);
            color: #27ae60;
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.15);
            color: #c0392b;
        }

        .empty-state {
            padding: 3rem 1.5rem;
            text-align: center;
        }

        .empty-state-icon {
            font-size: 3.5rem;
            color: #cbd5e1;
            margin-bottom: 1.5rem;
        }

        .empty-state-text {
            color: var(--light-text);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .back-button {
            margin-top: 1.5rem;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInDown 0.5s ease-out;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1.25rem;
            }

            .card-header {
                padding: 1.25rem;
            }

            .card-body {
                padding: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <div class="content-wrapper">
        <div class="page-header">
            <h1 class="page-title">Test Class Reminder</h1>
            <p class="page-subtitle">Create and test notification reminders for your upcoming classes</p>
        </div>';

// If creating a test notification
if ($create_test) {
    // Create a test notification for tomorrow's class
    $message = "⚠️ IMPORTANT: You have a class for Grade 10 (Mathematics) tomorrow at 09:00 AM.";

    $insert = $conn->prepare("
        INSERT INTO notifications
        (teacher_id, message, scheduled_time, is_read)
        VALUES (?, ?, NOW(), 0)
    ");
    $insert->bind_param("is", $teacher_id, $message);

    if ($insert->execute()) {
        echo '<div class="alert alert-success fade-in">
            <i class="fas fa-check-circle fa-lg"></i>
            <div>
                <strong>Success!</strong>
                <div>Test notification created successfully!</div>
            </div>
        </div>';
    } else {
        echo '<div class="alert alert-danger fade-in">
            <i class="fas fa-exclamation-circle fa-lg"></i>
            <div>
                <strong>Error!</strong>
                <div>Error creating test notification: ' . $conn->error . '</div>
            </div>
        </div>';
    }
    $insert->close();
}

// Get teacher's classes for tomorrow
$stmt = $conn->prepare("
    SELECT tt.*, s.subject_name
    FROM teacher_timetable tt
    LEFT JOIN subjects s ON tt.subject_id = s.subject_id
    WHERE tt.teacher_id = ? AND tt.day_of_week = ?
    ORDER BY tt.start_time
");
$stmt->bind_param("is", $teacher_id, $tomorrow);
$stmt->execute();
$result = $stmt->get_result();

echo '<div class="card">
    <div class="card-header primary-header">
        <i class="fas fa-calendar-alt me-2"></i> Your Classes for Tomorrow (' . $tomorrow . ')
    </div>
    <div class="card-body">';

if ($result->num_rows > 0) {
    echo '<ul class="list-group">';
    while ($row = $result->fetch_assoc()) {
        $subject = $row['subject_name'] ?? 'Unnamed Subject';
        echo '<li class="list-group-item">
            <div class="class-item">
                <div class="class-icon">
                    <i class="fas fa-book"></i>
                </div>
                <div class="class-details">
                    <div class="class-title">Grade ' . htmlspecialchars($row['grade']) . ' - ' . htmlspecialchars($subject) . '</div>
                    <div class="class-time">
                        <i class="far fa-clock"></i>
                        <span>' . date("h:i A", strtotime($row['start_time'])) . ' - ' . date("h:i A", strtotime($row['end_time'])) . '</span>
                    </div>
                </div>
            </div>
        </li>';
    }
    echo '</ul>';
} else {
    echo '<div class="empty-state">
        <div class="empty-state-icon">
            <i class="far fa-calendar-times"></i>
        </div>
        <h5 class="empty-state-text">No Classes Scheduled</h5>
        <p class="empty-state-subtext">You don\'t have any classes scheduled for tomorrow.</p>
    </div>';
}

echo '</div>
</div>

<div class="card">
    <div class="card-header info-header">
        <i class="fas fa-bell me-2"></i> Test Notification
    </div>
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-3">Create Test Notification</h5>
                <p class="mb-4">Click the button to create a test class reminder notification. This will help you verify that the notification system is working correctly.</p>
                <a href="?create=true" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-2"></i> Create Test Notification
                </a>
            </div>
            <div class="col-md-4 text-center d-none d-md-block">
                <div style="font-size: 5rem; color: var(--accent-color); opacity: 0.8;">
                    <i class="fas fa-bell"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="back-button">
    <a href="../../frontend/dashboards/teacher_dashboard.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
    </a>
</div>

</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        // Add hover effect to list items
        const listItems = document.querySelectorAll(".list-group-item");
        listItems.forEach(item => {
            item.addEventListener("mouseenter", function() {
                this.style.backgroundColor = "#f1f5f9";
                this.style.transform = "translateX(5px)";
            });

            item.addEventListener("mouseleave", function() {
                this.style.backgroundColor = "transparent";
                this.style.transform = "translateX(0)";
            });
        });

        // Auto-hide success message after 5 seconds
        const successAlert = document.querySelector(".alert-success");
        if (successAlert) {
            setTimeout(() => {
                successAlert.style.opacity = "0";
                successAlert.style.transform = "translateY(-20px)";
                successAlert.style.transition = "all 0.5s ease";

                setTimeout(() => {
                    successAlert.style.display = "none";
                }, 500);
            }, 5000);
        }
    });
</script>
</body>
</html>';

$stmt->close();
$conn->close();
?>
