<?php
session_start();

$currentPage = basename($_SERVER['PHP_SELF']);


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'worker') {
    header("Location: ../../index.php");
    exit();
}


include_once '../../backend/server/db_connect.php';


$month = isset($_GET['month']) ? intval($_GET['month']) : date('n');
$year = isset($_GET['year']) ? intval($_GET['year']) : date('Y');

$subjects_query = "SELECT DISTINCT subject_name FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_query);


$grades_query = "SELECT DISTINCT grade FROM students ORDER BY grade";
$grades_result = $conn->query($grades_query);


$subject_filter = isset($_GET['subject']) ? $_GET['subject'] : '';
$grade_filter = isset($_GET['grade']) ? $_GET['grade'] : '';


$query = "
    SELECT DISTINCT s.student_id, CONCAT(u.first_name, ' ', u.last_name) AS student_name,
           s.grade, sub.subject_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN student_subjects ss ON s.student_id = ss.student_id
    LEFT JOIN subjects sub ON ss.subject_id = sub.subject_id
    WHERE 1=1
";

if (!empty($subject_filter)) {
    $query .= " AND sub.subject_name = '$subject_filter'";
}

if (!empty($grade_filter)) {
    $query .= " AND s.grade = '$grade_filter'";
}

$query .= " ORDER BY s.grade, student_name";
$students_result = $conn->query($query);

// Get the number of days in the month
$days_in_month = cal_days_in_month(CAL_GREGORIAN, $month, $year);

// Calculate the weeks in the month
$weeks = [];
$current_week = 1;
for ($day = 1; $day <= $days_in_month; $day++) {
    $date = sprintf('%04d-%02d-%02d', $year, $month, $day);
    $week_number = ceil($day / 7);
    if (!isset($weeks[$week_number])) {
        $weeks[$week_number] = [
            'start' => $date,
            'end' => $date
        ];
    } else {
        $weeks[$week_number]['end'] = $date;
    }
}


$month_name = date('F', mktime(0, 0, 0, $month, 1, $year));
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Monthly Attendance</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #1e3c72;
      --secondary-color: #2a5298;
      --accent-color: #67BAFD;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-color: #f8f9fa;
      --dark-color: #343a40;
      --border-radius: 15px;
      --card-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      --transition-speed: 0.3s;
    }

    body {
      background-color: #f4f7fe;
      font-family: 'Poppins', sans-serif;
      margin: 0;
      padding: 0;
      color: #444;
    }

    .content {
      margin-left: 280px;
      padding: 30px;
      transition: all var(--transition-speed) ease;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .page-title {
      font-size: 1.8rem;
      font-weight: 600;
      color: var(--primary-color);
      margin: 0;
      display: flex;
      align-items: center;
    }

    .page-title i {
      margin-right: 12px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10px;
      font-size: 1.2rem;
    }

    .month-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      flex-wrap: wrap;
      gap: 15px;
    }

    .month-selector {
      display: flex;
      align-items: center;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      padding: 8px 15px;
    }

    .month-display {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--primary-color);
      margin: 0 15px;
      min-width: 150px;
      text-align: center;
    }

    .month-nav-btn {
      background: transparent;
      border: none;
      color: var(--primary-color);
      font-size: 1.1rem;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .month-nav-btn:hover {
      background: rgba(0, 0, 0, 0.05);
    }

    .filter-container {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      padding: 15px;
    }

    .filter-container .form-select {
      border-radius: 8px;
      border: 1px solid #e0e0e0;
      padding: 10px 15px;
      font-size: 0.95rem;
    }

    .filter-container .btn-primary {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border: none;
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 500;
    }

    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .download-btn {
      background: linear-gradient(135deg, #2ecc71, #27ae60);
      border: none;
      border-radius: 10px;
      padding: 12px 25px;
      color: white;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 10px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(46, 204, 113, 0.2);
    }

    .download-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 15px rgba(46, 204, 113, 0.3);
    }

    .attendance-table {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }

    .attendance-table .table {
      margin-bottom: 0;
    }

    .attendance-table th {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      font-weight: 500;
      text-align: center;
      vertical-align: middle;
      padding: 15px 10px;
      border: none;
    }

    .attendance-table td {
      text-align: center;
      vertical-align: middle;
      padding: 15px 10px;
      border-color: #f0f0f0;
    }

    .attendance-table tbody tr:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .present {
      color: var(--success-color);
      font-weight: 600;
    }

    .absent {
      color: var(--danger-color);
      font-weight: 600;
    }

    .mark-present-btn {
      background-color: rgba(46, 204, 113, 0.1);
      color: var(--success-color);
      border: 1px solid rgba(46, 204, 113, 0.3);
      border-radius: 6px;
      padding: 5px 10px;
      font-size: 0.85rem;
      transition: all 0.2s ease;
      margin-top: 5px;
    }

    .mark-present-btn:hover {
      background-color: rgba(46, 204, 113, 0.2);
      border-color: var(--success-color);
    }

    .student-name {
      font-weight: 600;
      color: var(--primary-color);
    }

    .student-id {
      color: #6c757d;
      font-size: 0.9rem;
    }

    .badge-grade {
      background-color: var(--primary-color);
      color: white;
      padding: 5px 10px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 0.85rem;
    }

    .badge-subject {
      background-color: var(--accent-color);
      color: white;
      padding: 5px 10px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 0.85rem;
    }

    .week-header {
      font-size: 0.9rem;
      font-weight: 600;
    }

    .week-dates {
      font-size: 0.8rem;
      opacity: 0.8;
    }

    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }

      .page-title {
        font-size: 1.5rem;
      }

      .month-nav {
        flex-direction: column;
        align-items: stretch;
      }

      .filter-container {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>

<?php include_once '../../assets/worker_sidebar.php'; ?>

<div class="content">
  <div class="page-header">
    <h1 class="page-title">
      <i class="fas fa-calendar-check"></i>
      <span>Monthly Attendance</span>
    </h1>
  </div>

  <div class="month-nav">
    <div class="month-selector">
      <a href="?month=<?= $month == 1 ? 12 : $month - 1 ?>&year=<?= $month == 1 ? $year - 1 : $year ?>&subject=<?= $subject_filter ?>&grade=<?= $grade_filter ?>" class="month-nav-btn">
        <i class="fas fa-chevron-left"></i>
      </a>
      <div class="month-display"><?= $month_name ?> <?= $year ?></div>
      <a href="?month=<?= $month == 12 ? 1 : $month + 1 ?>&year=<?= $month == 12 ? $year + 1 : $year ?>&subject=<?= $subject_filter ?>&grade=<?= $grade_filter ?>" class="month-nav-btn">
        <i class="fas fa-chevron-right"></i>
      </a>
    </div>

    <form method="GET" class="filter-container">
      <select name="subject" class="form-select">
        <option value="">All Subjects</option>
        <?php while ($subject = $subjects_result->fetch_assoc()): ?>
          <option value="<?= $subject['subject_name'] ?>" <?= $subject_filter == $subject['subject_name'] ? 'selected' : '' ?>>
            <?= $subject['subject_name'] ?>
          </option>
        <?php endwhile; ?>
      </select>

      <select name="grade" class="form-select">
        <option value="">All Grades</option>
        <?php while ($grade = $grades_result->fetch_assoc()): ?>
          <option value="<?= $grade['grade'] ?>" <?= $grade_filter == $grade['grade'] ? 'selected' : '' ?>>
            Grade <?= $grade['grade'] ?>
          </option>
        <?php endwhile; ?>
      </select>

      <input type="hidden" name="month" value="<?= $month ?>">
      <input type="hidden" name="year" value="<?= $year ?>">
      <button type="submit" class="btn btn-primary">
        <i class="fas fa-filter me-2"></i> Apply Filters
      </button>
    </form>
  </div>

  <!-- Action Bar -->
  <div class="action-bar">
    <div class="attendance-summary">
      <?php if ($students_result->num_rows > 0): ?>
        <div class="badge bg-info p-2">
          <i class="fas fa-users me-1"></i> <?= $students_result->num_rows ?> Students
        </div>
      <?php endif; ?>
    </div>

    <button id="downloadReportBtn" class="download-btn">
      <i class="fas fa-download"></i>
      <span>Download Report</span>
    </button>
  </div>

  <div class="attendance-table">
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th style="width: 10%">ID</th>
            <th style="width: 20%">Student</th>
            <th style="width: 10%">Grade</th>
            <th style="width: 15%">Subject</th>
            <?php foreach ($weeks as $week_num => $week_dates): ?>
              <th>
                <div class="week-header">Week <?= $week_num ?></div>
                <div class="week-dates"><?= date('d/m', strtotime($week_dates['start'])) ?> - <?= date('d/m', strtotime($week_dates['end'])) ?></div>
              </th>
            <?php endforeach; ?>
          </tr>
        </thead>
        <tbody>
          <?php if ($students_result->num_rows > 0): ?>
            <?php while ($student = $students_result->fetch_assoc()): ?>
              <tr>
                <td class="student-id"><?= $student['student_id'] ?></td>
                <td class="text-start student-name"><?= $student['student_name'] ?></td>
                <td><span class="badge-grade">Grade <?= $student['grade'] ?></span></td>
                <td><span class="badge-subject"><?= $student['subject_name'] ?></span></td>

                <?php foreach ($weeks as $week_num => $week_dates): ?>
                  <?php
                  // Check if student was present in this week
                  $week_start = $week_dates['start'];
                  $week_end = $week_dates['end'];

                  $attendance_query = "
                      SELECT * FROM attendance
                      WHERE student_id = {$student['student_id']}
                      AND date BETWEEN '$week_start' AND '$week_end'
                      LIMIT 1
                  ";
                  $attendance_result = $conn->query($attendance_query);
                  $status = $attendance_result->num_rows > 0 ? 'Present' : 'Absent';
                  $status_class = $status == 'Present' ? 'present' : 'absent';
                  ?>

                  <td class="<?= $status_class ?>">
                    <div><?= $status ?></div>
                    <?php if ($status == 'Absent'): ?>
                      <button class="mark-present-btn mark-present"
                              data-student-id="<?= $student['student_id'] ?>"
                              data-week="<?= $week_num ?>"
                              data-date="<?= $week_start ?>">
                        <i class="fas fa-check me-1"></i> Mark Present
                      </button>
                    <?php endif; ?>
                  </td>
                <?php endforeach; ?>
              </tr>
            <?php endwhile; ?>
          <?php else: ?>
            <tr>
              <td colspan="<?= 4 + count($weeks) ?>" class="text-center py-4">
                <div class="alert alert-info mb-0">
                  <i class="fas fa-info-circle me-2"></i>
                  No students found with the selected filters
                </div>
              </td>
            </tr>
          <?php endif; ?>
        </tbody>
      </table>
    </div>
  </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
$(document).ready(function() {
  // Handle marking a student as present
  $('.mark-present').click(function() {
    const studentId = $(this).data('student-id');
    const week = $(this).data('week');
    const date = $(this).data('date');
    const button = $(this);
    const cell = button.parent(); 

    console.log("Marking attendance with data:", {
      student_id: studentId,
      date: date,
      week: 'Week ' + week
    });

    // Send AJAX request to mark attendance
    $.ajax({
      url: '../../backend/attendance/manual_attendance.php',
      type: 'POST',
      data: {
        student_id: studentId,
        date: date,
        week: 'Week ' + week
      },
      dataType: 'json',
      success: function(response) {
        console.log("Success response:", response);
        if (response.success) {
          
          cell.removeClass('absent').addClass('present');
          cell.html('<div>Present</div>');

         
          const actionText = response.updated ? 'updated' : 'marked';

          
          const toast = `<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 5">
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
              <div class="toast-header bg-success text-white">
                <i class="fas fa-check-circle me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
              </div>
              <div class="toast-body">
                Attendance ${actionText} successfully!    //successful
              </div>
            </div>
          </div>`;

          $('body').append(toast);
          setTimeout(() => {
            $('.toast').toast('hide');
            setTimeout(() => $('.toast').parent().remove(), 500);
          }, 3000);

          // Update Today's Attendance counter 
          const today = new Date().toISOString().split('T')[0]; 
          console.log("Comparing dates:", { date, today });
          if (date === today) {
            console.log("Today's attendance marked, updating counter");
            
            if (window.opener && !window.opener.closed) {
              try {
                
                if (window.opener.location.href.includes('worker_dashboard.php')) {
                  
                  if (typeof window.opener.updateTodayAttendance === 'function') {
                    window.opener.updateTodayAttendance();
                  }
                }
              } catch (e) {
                console.log('Could not access opener window:', e);
              }
            }

           
            try {
              localStorage.setItem('attendance_updated', Date.now().toString());
            } catch (e) {
              console.log('Could not use localStorage:', e);
            }
          }
        } else {
          console.error("Error in response:", response.message);
          alert('Error: ' + response.message);
        }
      },
      error: function(xhr, status, error) {
        console.error("AJAX Error:", status, error);
        console.log("Response Text:", xhr.responseText);
        try {
          const errorResponse = JSON.parse(xhr.responseText);
          console.log("Parsed error response:", errorResponse);
          alert('Error: ' + (errorResponse.message || 'An error occurred while marking attendance. Please try again.'));
        } catch (e) {
          alert('An error occurred while marking attendance. Please try again.');
        }
      }
    });
  });

  // Handle download report button click
  $('#downloadReportBtn').click(function() {
    
    const month = <?= $month ?>;
    const year = <?= $year ?>;
    const subject = '<?= $subject_filter ?>';
    const grade = '<?= $grade_filter ?>';

    // Redirect to the PDF generation 
    window.location.href = `../../backend/attendance/generate_attendance_report.php?month=${month}&year=${year}&subject=${subject}&grade=${grade}`;
  });
});
</script>
</body>
</html>




