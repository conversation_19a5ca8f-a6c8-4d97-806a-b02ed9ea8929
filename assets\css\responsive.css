/* Responsive CSS for Gurukula LMS
   This file contains global responsive styles for the entire application
*/

/* Base responsive settings */
:root {
  --sidebar-width: 260px;
  --sidebar-width-collapsed: 70px;
}

/* Mobile menu toggle button */
.mobile-menu-toggle {
  display: none;
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 1050;
  background-color: var(--primary-color, #02005F);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 12px;
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
  background-color: var(--accent-color, #67BAFD);
}

/* Sidebar responsive behavior */
.sidebar {
  transition: all 0.3s ease;
}

.sidebar.collapsed {
  width: var(--sidebar-width-collapsed);
  padding: 25px 10px;
}

.sidebar.collapsed h3 span,
.sidebar.collapsed a span {
  display: none;
}

.sidebar.collapsed a {
  text-align: center;
  padding: 12px 5px;
}

.sidebar.collapsed a i {
  font-size: 1.5rem;
  margin-right: 0;
}

/* Content area responsive adjustments */
.content {
  transition: margin-left 0.3s ease;
}

/* Extra small devices (phones, 576px and down) */
@media (max-width: 576px) {
  .mobile-menu-toggle {
    display: block;
  }
  
  .sidebar {
    transform: translateX(-100%);
    z-index: 1040;
  }
  
  .sidebar.active {
    transform: translateX(0);
  }
  
  .content {
    margin-left: 0 !important;
    padding: 15px !important;
    padding-top: 60px !important;
  }
  
  .card, .dashboard-card {
    margin-bottom: 15px;
  }
  
  .page-title, h1 {
    font-size: 1.5rem !important;
  }
  
  .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
  }
  
  .table-responsive {
    font-size: 0.85rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .hero-text h1 {
    font-size: 2rem !important;
  }
  
  .hero-text h2 {
    font-size: 1.5rem !important;
  }
  
  .hero-img img {
    width: 100% !important;
    height: auto !important;
  }
}

/* Small devices (landscape phones, 576px to 768px) */
@media (min-width: 577px) and (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }
  
  .sidebar {
    transform: translateX(-100%);
    z-index: 1040;
  }
  
  .sidebar.active {
    transform: translateX(0);
  }
  
  .content {
    margin-left: 0 !important;
    padding: 20px !important;
    padding-top: 60px !important;
  }
  
  .hero-text h1 {
    font-size: 2.5rem !important;
  }
  
  .hero-img img {
    width: 100% !important;
    height: auto !important;
  }
}

/* Medium devices (tablets, 768px to 992px) */
@media (min-width: 769px) and (max-width: 992px) {
  .sidebar {
    width: var(--sidebar-width-collapsed);
    padding: 25px 10px;
  }
  
  .sidebar h3 span,
  .sidebar a span {
    display: none;
  }
  
  .sidebar a {
    text-align: center;
    padding: 12px 5px;
  }
  
  .sidebar a i {
    font-size: 1.5rem;
    margin-right: 0;
  }
  
  .content {
    margin-left: var(--sidebar-width-collapsed) !important;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 993px) {
  .sidebar {
    width: var(--sidebar-width);
  }
  
  .content {
    margin-left: var(--sidebar-width);
  }
}

/* Utility classes for responsive layouts */
.d-sm-none {
  display: none !important;
}

@media (min-width: 576px) {
  .d-sm-block {
    display: block !important;
  }
  
  .d-sm-none {
    display: none !important;
  }
}

/* Responsive grid adjustments */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

@media (max-width: 576px) {
  .responsive-grid {
    grid-template-columns: 1fr;
  }
}

/* Responsive table adjustments */
@media (max-width: 768px) {
  .responsive-table th, 
  .responsive-table td {
    padding: 0.5rem !important;
  }
  
  .responsive-table th {
    font-size: 0.85rem;
  }
  
  .responsive-table td {
    font-size: 0.9rem;
  }
}

/* Responsive form adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }
  
  .form-row > div {
    width: 100% !important;
    margin-right: 0 !important;
  }
}
