<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title><PERSON><PERSON><PERSON></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <link rel="stylesheet" href="../assets/css/responsive.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', sans-serif;
      background-color: #000;
      color: #fff;
      overflow-x: hidden;
      position: relative;
    }

    canvas#particleCanvas {
      position: fixed;
      top: 0;
      left: 0;
      z-index: -1;
    }

    .hero, .section, footer {
      position: relative;
      z-index: 1;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    nav.navbar {
      background-color: rgba(0, 14, 46, 0.9);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 15px 0;
      transition: all 0.3s ease;
    }

    nav.navbar.scrolled {
      padding: 10px 0;
      background-color: rgba(0, 14, 46, 0.95);
    }

    .navbar-brand {
      font-weight: 700;
      font-size: 1.4rem;
      position: relative;
      padding-left: 15px;
    }

    .navbar-brand::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 70%;
      background: linear-gradient(to bottom, #FFD700, #FFC107);
      border-radius: 2px;
    }

    .hero {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 80px 50px;
      flex-wrap: wrap;
    }

    .hero-text {
      flex: 1;
      min-width: 300px;
      margin-bottom: 30px;
    }

    .hero-text h1 {
      font-size: 3.5rem;
      font-weight: 700;
      color: #fff;
      border: 2px solid rgba(255, 255, 255, 0.2);
      padding: 10px 20px;
      border-radius: 12px;
      display: inline-block;
      box-shadow: 0 0 20px rgba(255, 255, 255, 0.15);
      transition: box-shadow 0.3s ease;
    }

    .hero-text h1:hover {
      box-shadow: 0 0 30px rgba(255, 255, 255, 0.4);
    }

    .hero-text h2 {
      color: #ccc;
    }

    .hero-img {
      flex: 1;
      min-width: 300px;
      text-align: center;
    }

    .hero-img img {
      width: 100%;
      max-width: 600px;
      height: auto;
      border-radius: 50%;
      object-fit: cover;
      border: none;
      box-shadow: 0 0 40px rgba(255, 255, 255, 0.1);
    }

    .section {
      padding: 60px 40px;
    }

    .section h2 {
      color: #67BAFD;
      margin-bottom: 30px;
    }

    .tutor-img {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      object-fit: cover;
      margin: 10px;
      border: 2px solid #fff;
    }

    footer {
      background-color: #000e2e;
      padding: 20px;
      text-align: center;
      color: #aaa;
    }

    .navbar-nav {
      gap: 5px;
    }

    .navbar-nav .nav-link {
      color: rgba(255, 255, 255, 0.85) !important;
      font-weight: 500;
      margin-right: 5px;
      padding: 8px 15px;
      border-radius: 8px;
      position: relative;
      transition: all 0.3s ease;
    }

    .navbar-nav .nav-link::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background: linear-gradient(to right, #FFD700, #FFC107);
      transition: all 0.3s ease;
      transform: translateX(-50%);
      opacity: 0;
    }

    .navbar-nav .nav-link:hover {
      color: white !important;
      background-color: rgba(255, 255, 255, 0.1);
    }

    .navbar-nav .nav-link:hover::after {
      width: 70%;
      opacity: 1;
    }

    .navbar-nav .nav-link.active {
      color: white !important;
      background-color: rgba(255, 255, 255, 0.15);
    }

    .navbar-nav .nav-link.active::after {
      width: 70%;
      opacity: 1;
    }

    .nav-item .btn-login {
      padding: 10px 24px;
      font-size: 0.95rem;
      font-weight: 600;
      color: white;
      background: linear-gradient(135deg, #FFD700, #FFC107);
      border: none;
      border-radius: 30px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .nav-item .btn-login:hover {
      background: linear-gradient(135deg, #FFC107, #FFD700);
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
    }

    .navbar-toggler {
      border: none;
      padding: 10px;
      outline: none;
      box-shadow: none;
    }

    .navbar-toggler:focus {
      box-shadow: none;
    }

    .navbar-toggler-icon {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    }

    /* Responsive styles */
    @media (max-width: 992px) {
      .hero {
        flex-direction: column;
        padding: 50px 30px;
      }

      .hero-text {
        text-align: center;
        margin-bottom: 40px;
      }

      .hero-text h1 {
        font-size: 2.8rem;
      }

      .hero-img img {
        max-width: 400px;
      }
    }

    @media (max-width: 768px) {
      .hero {
        padding: 40px 20px;
      }

      .hero-text h1 {
        font-size: 2.2rem;
        padding: 8px 16px;
      }

      .hero-text h2 {
        font-size: 1.5rem;
      }

      .section {
        padding: 40px 20px;
      }

      .tutor-img {
        width: 120px;
        height: 120px;
      }
    }

    @media (max-width: 576px) {
      .hero-text h1 {
        font-size: 1.8rem;
      }

      .hero-img img {
        max-width: 300px;
      }

      .tutor-img {
        width: 100px;
        height: 100px;
      }
    }
  </style>
</head>
<body>

<nav class="navbar navbar-expand-lg navbar-dark fixed-top">
  <div class="container">
    <a class="navbar-brand text-white" href="#">
      Gurukula LMS
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav ms-auto align-items-center">
        <li class="nav-item">
          <a class="nav-link active" href="#">
            <i class="fas fa-home me-1"></i> Home
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#about">
            <i class="fas fa-info-circle me-1"></i> About Us
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#tutors">
            <i class="fas fa-chalkboard-teacher me-1"></i> Tutors
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#location">
            <i class="fas fa-map-marker-alt me-1"></i> Location
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#contact">
            <i class="fas fa-envelope me-1"></i> Contact
          </a>
        </li>
        <li class="nav-item ms-lg-3 mt-2 mt-lg-0">
          <a class="btn btn-login" href="../frontend/login.php">
            <i class="fas fa-sign-in-alt me-1"></i> Login
          </a>
        </li>
      </ul>
    </div>
  </div>
</nav>

<!-- Add padding to body to account for fixed navbar -->
<div style="padding-top: 80px;"></div>

<section class="hero">
  <div class="hero-text">
    <h1>Welcome!!!</h1>
    <h2>GURUKULA Institution</h2>
  </div>
  <div class="hero-img">
    <img src="../assets/home2.jpg" alt="home2">
  </div>
</section>

<section id="about" class="section">
  <div class="container">
    <h2>About Us</h2>
    <p>We are committed to providing the best online and offline tutoring support to students across grades. Our mission is to connect passionate educators with eager learners in a dynamic environment.</p>
  </div>
</section>

<section id="tutors" class="section bg-dark">
  <div class="container text-center">
    <h2>Our Tutors</h2>
    <div class="d-flex justify-content-center flex-wrap">
      <div><img src="../assets/default.jpg" class="tutor-img" alt="Tutor 1"><p class="text-light">Ms.Udaya</p></div>
      <div><img src="../assets/tutor1.jpeg" class="tutor-img" alt="Tutor 2"><p class="text-light">Ms. Perera</p></div>
      <div><img src="../assets/tutor2.jpeg" class="tutor-img" alt="Tutor 3"><p class="text-light">Mr. Saman</p></div>
    </div>
  </div>
</section>

<section id="location" class="section">
  <div class="container">
    <h2>Our Location</h2>
    <div class="ratio ratio-16x9">
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d63470.033046759105!2d80.6506334406795!3d6.1472089129280985!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3ae151de7f163239%3A0x2ad4c9f9308eeb61!2sWalasmulla!5e0!3m2!1sen!2slk!4v1747650491092!5m2!1sen!2slk" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
    </div>
  </div>
</section>

<section id="contact" class="section bg-dark">
  <div class="container">
    <h2>Contact Us</h2>
    <p>   Email: <EMAIL> </p>
    <p>   Phone: 0 77 123 4567      </p>
    <p>   Address: Weeraketiya Road,Walasmulla. </p>


  </div>
</section>

<footer>
  &copy; <?= date('Y') ?> Gurukula,Learning Management System. All rights reserved.
</footer>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<canvas id="particleCanvas"></canvas>

<script>
  // Navbar scroll effect
  window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });

  // Active link 
  document.addEventListener('DOMContentLoaded', function() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    function highlightNavLink() {
      let scrollPosition = window.scrollY + 100;

      sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');

        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
          navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + sectionId) {
              link.classList.add('active');
            }
          });
        }
      });

      
      if (scrollPosition < 100) {
        navLinks.forEach(link => {
          link.classList.remove('active');
          if (link.getAttribute('href') === '#') {
            link.classList.add('active');
          }
        });
      }
    }

    window.addEventListener('scroll', highlightNavLink);
    highlightNavLink();

    // Smooth scrolling for anchor links
    navLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        const href = this.getAttribute('href');

        if (href.startsWith('#')) {
          e.preventDefault();

          const targetId = href === '#' ? 'body' : href;
          const targetElement = targetId === 'body' ? document.body : document.querySelector(targetId);

          if (targetElement) {
            window.scrollTo({
              top: targetId === 'body' ? 0 : targetElement.offsetTop - 70,
              behavior: 'smooth'
            });

            const navbarCollapse = document.querySelector('.navbar-collapse');
            if (navbarCollapse.classList.contains('show')) {
              const bsCollapse = new bootstrap.Collapse(navbarCollapse);
              bsCollapse.hide();
            }
          }
        }
      });
    });
  });

  // Particle animation
  const canvas = document.getElementById('particleCanvas');
  const ctx = canvas.getContext('2d');
  let particles = [];

  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;

  const colors = ['#FFD700', '#FFEA00', '#FFC107']; // various gold shades

  const createParticles = (num) => {
    for (let i = 0; i < num; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.6,
        vy: (Math.random() - 0.5) * 0.6,
        radius: Math.random() * 2 + 1,
        color: colors[Math.floor(Math.random() * colors.length)]
      });
    }
  };

  const drawParticles = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw lines
    for (let i = 0; i < particles.length; i++) {
      for (let j = i + 1; j < particles.length; j++) {
        const dx = particles[i].x - particles[j].x;
        const dy = particles[i].y - particles[j].y;
        const dist = Math.sqrt(dx * dx + dy * dy);
        if (dist < 100) {
          ctx.strokeStyle = 'rgba(255, 215, 0, 0.15)'; 
          ctx.lineWidth = 1;
          ctx.beginPath();
          ctx.moveTo(particles[i].x, particles[i].y);
          ctx.lineTo(particles[j].x, particles[j].y);
          ctx.stroke();
        }
      }
    }

    // Draw particles
    particles.forEach(p => {
      ctx.beginPath();
      ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
      ctx.fillStyle = p.color;
      ctx.fill();
    });
  };

  const updateParticles = () => {
    particles.forEach(p => {
      p.x += p.vx;
      p.y += p.vy;

      if (p.x < 0 || p.x > canvas.width) p.vx *= -1;
      if (p.y < 0 || p.y > canvas.height) p.vy *= -1;
    });
  };

  const animate = () => {
    drawParticles();
    updateParticles();
    requestAnimationFrame(animate);
  };

  createParticles(100);
  animate();

  window.addEventListener('resize', () => {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
  });
</script>

</body>
</html>
