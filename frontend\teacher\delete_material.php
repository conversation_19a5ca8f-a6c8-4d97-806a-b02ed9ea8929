<?php
session_start();
include '../../backend/server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}


if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header("Location: subject_details.php?error=invalid_id");
    exit();
}

$material_id = $_GET['id'];
$user_id = $_SESSION['user_id'];


$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$teacher_id = $teacher['teacher_id'];
$stmt->close();

// Verify that the material 
$stmt = $conn->prepare("SELECT file_path FROM materials WHERE id = ? AND teacher_id = ?");
$stmt->bind_param("ii", $material_id, $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    
    header("Location: subject_details.php?error=unauthorized");
    exit();
}


$material = $result->fetch_assoc();
$file_path = '../../' . $material['file_path'];
$stmt->close();


$stmt = $conn->prepare("DELETE FROM materials WHERE id = ? AND teacher_id = ?");
$stmt->bind_param("ii", $material_id, $teacher_id);
$success = $stmt->execute();
$stmt->close();


if ($success && file_exists($file_path)) {
    unlink($file_path); // Delete the file
}


header("Location: subject_details.php?success=material_deleted");
exit();
?>