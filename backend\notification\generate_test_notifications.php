<?php
include_once '../../backend/server/db_connect.php';

// Check if notifications table exists
$result = $conn->query("SHOW TABLES LIKE 'notifications'");
if ($result->num_rows == 0) {
    echo "Notifications table doesn't exist. Please run setup_notifications.php first.<br>";
    exit;
}

// Get all teachers
$teacherResult = $conn->query("SELECT teacher_id FROM teachers");
if ($teacherResult->num_rows == 0) {
    echo "No teachers found in the database.<br>";
    exit;
}

// Store teacher IDs
$teacherIds = [];
while ($teacher = $teacherResult->fetch_assoc()) {
    $teacherIds[] = $teacher['teacher_id'];
}

// Get all subjects
$subjectResult = $conn->query("SELECT subject_id, subject_name FROM subjects");
if ($subjectResult->num_rows == 0) {
    echo "No subjects found in the database.<br>";
    exit;
}

// Store subjects
$subjects = [];
while ($subject = $subjectResult->fetch_assoc()) {
    $subjects[] = $subject;
}

// Clear existing notifications
$conn->query("DELETE FROM notifications");
echo "Cleared existing notifications.<br>";

// Generate random notifications
$notificationMessages = [
    "New question in {subject} needs your attention",
    "A student has asked a question about {subject}",
    "You have an unanswered question in {subject}",
    "Please check the forum for a new question in {subject}",
    "Student waiting for your response in {subject}"
];

$insertCount = 0;
$numNotifications = isset($_GET['count']) ? intval($_GET['count']) : 5;
$numNotifications = min(max($numNotifications, 1), 20); // Limit between 1 and 20

for ($i = 0; $i < $numNotifications; $i++) {
    // Pick a random teacher
    $teacherId = $teacherIds[array_rand($teacherIds)];
    
    // Pick a random subject
    $subject = $subjects[array_rand($subjects)];
    
    // Pick a random message template
    $messageTemplate = $notificationMessages[array_rand($notificationMessages)];
    
    // Replace {subject} with actual subject name
    $message = str_replace("{subject}", $subject['subject_name'], $messageTemplate);
    
    // Generate a random timestamp within the last 7 days
    $timestamp = date('Y-m-d H:i:s', time() - rand(0, 7 * 24 * 60 * 60));
    
    $stmt = $conn->prepare("
        INSERT INTO notifications (teacher_id, message, created_at) 
        VALUES (?, ?, ?)
    ");
    $stmt->bind_param("iss", $teacherId, $message, $timestamp);
    
    if ($stmt->execute()) {
        $insertCount++;
    }
}

echo "Created $insertCount test notifications.<br>";
echo "Notification generation complete.";
?>
