# Server Monitoring System - Guru<PERSON>la LMS

This document describes the server monitoring system implemented to handle XAMPP server downtime gracefully.

## Overview

The server monitoring system provides:
- Automatic detection when XAMPP/MySQL services are down
- User-friendly error pages with status information
- Real-time monitoring with notifications
- Automatic redirects to appropriate error pages
- Auto-refresh functionality to check when services come back online

## Components

### 1. Database Connection Handler (`backend/server/db_connect.php`)
- Enhanced with server status checking
- Automatically redirects to error pages when services are unavailable
- Distinguishes between MySQL server down vs database not found

### 2. Error Pages
- **`server_error.php`** - Displayed when MySQL server is completely down
- **`database_error.php`** - Displayed when MySQL is running but database is missing

### 3. Health Check Endpoint (`backend/server/health_check.php`)
- JSON API endpoint for checking server status
- Returns detailed status of MySQL server and database
- Used by JavaScript monitoring for real-time checks

### 4. JavaScript Monitor (`assets/js/server_monitor.js`)
- Client-side monitoring with configurable intervals
- Shows notifications when connection is lost/restored
- Automatically redirects to error pages after multiple failed checks
- Includes visual notifications with different types (success, warning, error)

### 5. Main Entry Point (`index.php`)
- Checks server status before redirecting to application
- Serves as the main entry point for the application

## Features

### Automatic Error Detection
- Checks MySQL server connectivity
- Verifies database availability
- Handles both server down and database missing scenarios

### User-Friendly Error Pages
- Modern, responsive design
- Clear status indicators
- Troubleshooting information
- Auto-refresh functionality
- Quick action buttons (retry, go home)

### Real-Time Monitoring
- Periodic health checks (default: every 60 seconds)
- Visual notifications for status changes
- Automatic error page redirection
- Tab visibility detection (checks when tab becomes active)

### Responsive Design
- Mobile-friendly error pages
- Animated status indicators
- Professional styling with gradients and animations

## Usage

### Automatic Integration
The monitoring system is automatically active on pages that include the server monitor script:

```html
<script src="../assets/js/server_monitor.js"></script>
```

### Manual Integration
You can also manually initialize the monitor with custom options:

```javascript
const monitor = new ServerMonitor({
    checkInterval: 30000,        // Check every 30 seconds
    showNotifications: true,     // Show status notifications
    redirectOnError: true        // Auto-redirect on errors
});
```

### Testing
Use the test page to verify monitoring functionality:
```
http://localhost/gurukula_lms/test_server_monitor.php
```

## Configuration

### Monitoring Intervals
- Default check interval: 60 seconds
- Error page auto-refresh: 30 seconds
- Notification auto-hide: 5 seconds

### Customization
You can customize the monitoring behavior by modifying:
- Check intervals in `server_monitor.js`
- Error page styling in the CSS sections
- Health check logic in `health_check.php`

## Error Scenarios Handled

1. **XAMPP Not Started**
   - MySQL service not running
   - Shows server error page
   - Provides instructions to start XAMPP

2. **Database Missing**
   - MySQL running but database not found
   - Shows database error page
   - Provides troubleshooting steps

3. **Network Issues**
   - Connection timeouts
   - Shows appropriate notifications
   - Attempts automatic recovery

## Files Modified/Added

### New Files
- `server_error.php` - Server down error page
- `database_error.php` - Database error page
- `backend/server/health_check.php` - Health check API
- `assets/js/server_monitor.js` - Client-side monitoring
- `index.php` - Main entry point with status checking
- `test_server_monitor.php` - Testing interface
- `.htaccess` - Apache configuration with error handling

### Modified Files
- `backend/server/db_connect.php` - Enhanced with status checking
- `frontend/home.php` - Added server monitor script
- `frontend/login.php` - Added server monitor script

## Best Practices

1. **Include Monitor Script**: Add the server monitor script to all main pages
2. **Test Regularly**: Use the test page to verify functionality
3. **Monitor Logs**: Check browser console for monitoring activity
4. **Customize Messages**: Update error messages to match your environment
5. **Performance**: Adjust check intervals based on your needs

## Troubleshooting

### Monitor Not Working
1. Check if `server_monitor.js` is loaded
2. Verify health check endpoint is accessible
3. Check browser console for JavaScript errors

### Error Pages Not Showing
1. Verify `.htaccess` configuration
2. Check file permissions
3. Ensure error pages are accessible

### False Positives
1. Adjust check intervals if too frequent
2. Verify network connectivity
3. Check MySQL service status

## Future Enhancements

Potential improvements:
- Email notifications for administrators
- Detailed logging system
- Service restart automation
- Multiple database support
- Advanced health metrics
- Integration with monitoring tools

## Support

For issues or questions about the server monitoring system:
1. Check the test page for diagnostics
2. Review browser console logs
3. Verify XAMPP service status
4. Check file permissions and paths
