<?php include_once __DIR__ . '/../../backend/student/student_detail.php'; ?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>My Profile</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    .profile-img {
      width: 140px;
      height: 140px;
      object-fit: cover;
      border-radius: 50%;
      border: 3px solid gold;
      cursor: pointer;
      transition: 0.3s;
    }
    .profile-img:hover {
      opacity: 0.8;
    }
    .profile-card {
      background: #fff;
      padding: 30px;
      border-radius: 16px;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      max-width: 600px;

    }
  </style>
</head>
<body class="bg-light">

<?php include_once __DIR__ . '/../../assets/student_sidebar.php'; ?>

<div class="container mt-5">
  <div class="profile-card mx-auto text-center">
    <!-- Clickable Image Upload -->
    <form action="upload_student_image.php" method="POST" enctype="multipart/form-data" id="imageForm">
      <input type="file" name="profile_image" id="profileInput" class="d-none" accept="image/*" onchange="document.getElementById('imageForm').submit();">
      <img src="<?= htmlspecialchars($imagePath) ?>" alt="Profile" class="profile-img" onclick="document.getElementById('profileInput').click();">
    </form>
    <h4 class="mt-3"><?= htmlspecialchars($student['user_name']) ?></h4>
    <p><strong>Email:</strong> <?= htmlspecialchars($student['email']) ?></p>
    <p><strong>Grade:</strong> <?= htmlspecialchars($student['grade']) ?></p>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
