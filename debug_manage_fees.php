<?php
session_start();
include_once 'backend/server/db_connect.php';

// Check if user is logged in as teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get teacher_id
$stmt = $conn->prepare("SELECT teacher_id, first_name, last_name FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_id = null;
$teacher_name = "";
if ($row = $result->fetch_assoc()) {
    $teacher_id = $row['teacher_id'];
    $teacher_name = $row['first_name'] . ' ' . $row['last_name'];
}
$stmt->close();

echo "<h1>Teacher Subject Debug for Fee Management</h1>";
echo "<p>Teacher ID: $teacher_id</p>";
echo "<p>Teacher Name: $teacher_name</p>";
echo "<p>User ID: $user_id</p>";

// Get raw data from teacher_subjects table
$stmt = $conn->prepare("
    SELECT ts.id, ts.teacher_id, ts.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

echo "<h2>Raw Teacher-Subject Relationships</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Teacher ID</th><th>Subject ID</th><th>Subject Name</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['teacher_id'] . "</td>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
}
echo "</table>";
$stmt->close();

// Get data with DISTINCT
$stmt = $conn->prepare("
    SELECT DISTINCT ts.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();

echo "<h2>After DISTINCT Query</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
}
echo "</table>";
$stmt->close();

// Get all subjects from the subjects table
$result = $conn->query("SELECT subject_id, subject_name FROM subjects ORDER BY subject_name");

echo "<h2>All Subjects in Database</h2>";
echo "<table border='1'>";
echo "<tr><th>Subject ID</th><th>Subject Name</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Now let's simulate the code in manage_fees.php
echo "<h2>Simulating manage_fees.php Processing</h2>";

// Get all subjects this teacher teaches (using DISTINCT)
$stmt = $conn->prepare("
    SELECT DISTINCT ts.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_subjects = [];

echo "<p>Building teacher_subjects array:</p>";
echo "<table border='1'>";
echo "<tr><th>Index</th><th>Subject ID</th><th>Subject Name</th></tr>";
$index = 0;
while ($row = $result->fetch_assoc()) {
    $teacher_subjects[] = [
        'subject_id' => $row['subject_id'],
        'subject_name' => $row['subject_name'],
        'fees' => [] // Will be populated with fees for each grade
    ];
    
    echo "<tr>";
    echo "<td>" . $index . "</td>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "<td>" . $row['subject_name'] . "</td>";
    echo "</tr>";
    $index++;
}
echo "</table>";
$stmt->close();

echo "<h2>Final teacher_subjects Array</h2>";
echo "<pre>";
print_r($teacher_subjects);
echo "</pre>";

echo "<p><a href='frontend/teacher/manage_fees.php'>Go to Manage Fees Page</a></p>";

$conn->close();
?>
