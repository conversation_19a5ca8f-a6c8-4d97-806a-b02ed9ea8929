<?php
include 'backend/server/db_connect.php';

// Check if subject_id column already exists
$result = $conn->query("SHOW COLUMNS FROM assignments LIKE 'subject_id'");
$exists = ($result->num_rows > 0);

if (!$exists) {
    // Add subject_id column to assignments table
    $sql = "ALTER TABLE assignments ADD COLUMN subject_id INT(11) AFTER teacher_id";
    
    if ($conn->query($sql) === TRUE) {
        echo "Column subject_id added successfully to assignments table.\n";
    } else {
        echo "Error adding column: " . $conn->error . "\n";
    }
} else {
    echo "Column subject_id already exists in assignments table.\n";
}

// Close connection
$conn->close();
?>
