<?php
// Include database connection
include_once 'backend/server/db_connect.php';

// Get all students
$students = $conn->query("SELECT s.student_id, CONCAT(u.first_name, ' ', u.last_name) as student_name 
                         FROM students s 
                         JOIN users u ON s.user_id = u.id");

echo "<h2>Student Marks Summary</h2>";

if ($students->num_rows > 0) {
    while ($student = $students->fetch_assoc()) {
        $student_id = $student['student_id'];
        $student_name = $student['student_name'];
        
        echo "<h3>Student: " . htmlspecialchars($student_name) . " (ID: " . $student_id . ")</h3>";
        
        // Get subjects for this student
        $subjects = $conn->query("SELECT s.subject_id, s.subject_name 
                                 FROM subjects s
                                 JOIN student_subjects ss ON s.subject_id = ss.subject_id
                                 WHERE ss.student_id = " . $student_id);
        
        if ($subjects->num_rows > 0) {
            echo "<table border='1'>";
            echo "<tr><th>Subject</th><th>Assignment</th><th>Marks</th><th>Comments</th></tr>";
            
            $total_marks = 0;
            $marks_count = 0;
            
            while ($subject = $subjects->fetch_assoc()) {
                $subject_id = $subject['subject_id'];
                $subject_name = $subject['subject_name'];
                
                // Get assignments and marks for this subject
                $query = "SELECT a.title, s.marks, s.comments 
                         FROM assignments a
                         LEFT JOIN submissions s ON a.assignment_id = s.assignment_id AND s.student_id = " . $student_id . "
                         WHERE a.subject_id = " . $subject_id;
                
                $assignments = $conn->query($query);
                
                if ($assignments->num_rows > 0) {
                    $subject_total = 0;
                    $subject_count = 0;
                    
                    while ($assignment = $assignments->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($subject_name) . "</td>";
                        echo "<td>" . htmlspecialchars($assignment['title']) . "</td>";
                        echo "<td>" . ($assignment['marks'] ? htmlspecialchars($assignment['marks']) : 'Not graded') . "</td>";
                        echo "<td>" . ($assignment['comments'] ? htmlspecialchars($assignment['comments']) : '-') . "</td>";
                        echo "</tr>";
                        
                        if ($assignment['marks']) {
                            $subject_total += $assignment['marks'];
                            $subject_count++;
                        }
                    }
                    
                    if ($subject_count > 0) {
                        $subject_avg = round($subject_total / $subject_count);
                        $total_marks += $subject_avg;
                        $marks_count++;
                        
                        echo "<tr style='background-color: #f0f0f0;'>";
                        echo "<td colspan='2'><strong>Average for " . htmlspecialchars($subject_name) . "</strong></td>";
                        echo "<td><strong>" . $subject_avg . "</strong></td>";
                        echo "<td></td>";
                        echo "</tr>";
                    }
                } else {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($subject_name) . "</td>";
                    echo "<td colspan='3'>No assignments found</td>";
                    echo "</tr>";
                }
            }
            
            if ($marks_count > 0) {
                $overall_avg = round($total_marks / $marks_count);
                
                echo "<tr style='background-color: #e0e0e0;'>";
                echo "<td colspan='2'><strong>Overall Average</strong></td>";
                echo "<td><strong>" . $overall_avg . "</strong></td>";
                echo "<td></td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>No subjects found for this student.</p>";
        }
        
        echo "<hr>";
    }
} else {
    echo "<p>No students found.</p>";
}

$conn->close();
?>
