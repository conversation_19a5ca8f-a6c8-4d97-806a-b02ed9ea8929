<?php
session_start();

$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';

// Add debugging
error_log("Marking student notifications as read");

header('Content-Type: application/json');

$user_id = $_SESSION['user_id'] ?? 0;
$role = $_SESSION['role'] ?? '';

error_log("User ID: $user_id, Role: $role");

if ($role !== 'student') {
    error_log("Not a student, returning error");
    echo json_encode(['success' => false, 'message' => 'Not authorized']);
    exit();
}

// Get student_id
$stmt = $conn->prepare("SELECT student_id FROM students WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();
$student_id = $student['student_id'] ?? 0;
$stmt->close();

error_log("Student ID: $student_id");

if ($student_id) {
    // Check if student_notifications table exists
    $tableExists = false;
    $result = $conn->query("SHOW TABLES LIKE 'student_notifications'");
    if ($result->num_rows > 0) {
        $tableExists = true;
    }

    if (!$tableExists) {
       
        $sql = "CREATE TABLE IF NOT EXISTS student_notifications (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id INT(11) NOT NULL,
            message VARCHAR(255) NOT NULL,
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            question_id INT(11) NULL
        )";

        if ($conn->query($sql) === TRUE) {
            error_log("Student notifications table created successfully");
        } else {
            error_log("Error creating student notifications table: " . $conn->error);
        }
    }

    // Mark all notifications as read for this student
    $q = $conn->prepare("UPDATE student_notifications SET is_read = 1 WHERE student_id = ? AND is_read = 0");
    $q->bind_param("i", $student_id);
    $result = $q->execute();

    $affected_rows = $conn->affected_rows;
    error_log("Marked $affected_rows notifications as read");

    if ($result) {
        // Also mark all questions as seen (not new replies anymore)
        $q = $conn->prepare("UPDATE questions SET is_new_reply = 0 WHERE student_id = ? AND is_new_reply = 1");
        $q->bind_param("i", $student_id);
        $q->execute();
        
        echo json_encode(['success' => true, 'message' => 'Notifications marked as read', 'count' => $affected_rows]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to mark notifications as read']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Student not found']);
}
?>
