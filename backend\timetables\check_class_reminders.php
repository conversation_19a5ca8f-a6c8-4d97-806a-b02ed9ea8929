<?php
session_start();
include_once '../../backend/server/db_connect.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Check if user is logged in as student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get student information including notification preferences
    $stmt = $conn->prepare("
        SELECT s.student_id, s.grade, s.email_notifications, s.popup_notifications, u.email
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE s.user_id = ?
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception("No student record found for this user");
    }

    $student = $result->fetch_assoc();
    $student_id = $student['student_id'];
    $grade = $student['grade'];

    // Format preferences for the frontend
    $preferences = [
        'student_id' => $student_id,
        'email_notifications' => $student['email_notifications'],
        'browser_notifications' => $student['popup_notifications'],
        'reminder_timing' => '1', // Default to 1 day before
        'notification_email' => $student['email']
    ];

    // Get current day of week
    $current_day = date('l');
    $reminder_days = 1; // Default to 1 day before

    // Calculate the day to check based on reminder timing
    $days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    $current_day_index = array_search($current_day, $days_of_week);

    if ($reminder_days === 0) {
        // Check classes for today
        $days_to_check = [$current_day];
    } else {
        // Check classes for future days based on reminder timing
        $days_to_check = [];
        for ($i = 1; $i <= $reminder_days; $i++) {
            $future_day_index = ($current_day_index + $i) % 7;
            $days_to_check[] = $days_of_week[$future_day_index];
        }
    }

    // Get timetable entries for the specified days
    $reminders = [];

    foreach ($days_to_check as $day) {
        $stmt = $conn->prepare("
            SELECT tt.day_of_week as day, s.subject_name as subject,
                   CONCAT(u.first_name, ' ', u.last_name) as teacher_name,
                   CONCAT(DATE_FORMAT(tt.start_time, '%h:%i %p'), ' - ', DATE_FORMAT(tt.end_time, '%h:%i %p')) as time
            FROM teacher_timetable tt
            JOIN subjects s ON tt.subject_id = s.subject_id
            JOIN teachers t ON tt.teacher_id = t.teacher_id
            JOIN users u ON t.user_id = u.id
            WHERE tt.grade = ? AND tt.day_of_week = ?
            ORDER BY tt.start_time
        ");
        $stmt->bind_param("ss", $grade, $day);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $reminders[] = $row;
        }
    }

    // Send email notifications if enabled
    if ($preferences['email_notifications'] === '1' && !empty($reminders)) {
        $email = $student['email'];

        if (!empty($email)) {
            // Prepare email content
            $subject = "Upcoming Class Reminders - Gurukula LMS";

            $message = "<html><body>";
            $message .= "<h2>Upcoming Class Reminders</h2>";
            $message .= "<p>Hello,</p>";
            $message .= "<p>Here are your upcoming classes:</p>";
            $message .= "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse;'>";
            $message .= "<tr><th>Day</th><th>Subject</th><th>Teacher</th><th>Time</th></tr>";

            foreach ($reminders as $reminder) {
                $message .= "<tr>";
                $message .= "<td>{$reminder['day']}</td>";
                $message .= "<td>{$reminder['subject']}</td>";
                $message .= "<td>{$reminder['teacher_name']}</td>";
                $message .= "<td>{$reminder['time']}</td>";
                $message .= "</tr>";
            }

            $message .= "</table>";
            $message .= "<p>Please be prepared for your classes.</p>";
            $message .= "<p>Regards,<br>Gurukula LMS Team</p>";
            $message .= "</body></html>";

            // Set email headers
            $headers = "MIME-Version: 1.0" . "\r\n";
            $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
            $headers .= "From: <EMAIL>" . "\r\n";

            // Send email
            mail($email, $subject, $message, $headers);

            // Log the email notification
            $log_stmt = $conn->prepare("
                INSERT INTO notification_logs
                (student_id, notification_type, subject, message, status)
                VALUES (?, 'email', ?, ?, 'sent')
            ");

            $log_subject = "Class Reminder Email";
            $log_message = "Email sent with " . count($reminders) . " upcoming class reminders for " . implode(', ', $days_to_check);

            $log_stmt->bind_param("iss", $student_id, $log_subject, $log_message);
            $log_stmt->execute();
        }
    }

    echo json_encode([
        'success' => true,
        'reminders' => $reminders,
        'preferences' => $preferences,
        'days_checked' => $days_to_check
    ]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

$conn->close();
?>
