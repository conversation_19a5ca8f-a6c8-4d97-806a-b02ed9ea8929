<?php
session_start();
include_once '../../backend/server/db_connect.php';


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];
$success_message = '';
$error_message = '';


$available_grades = ["Grade 6", "Grade 7", "Grade 8", "Grade 9", "Grade 10", "Grade 11", "Grade 12", "A/L"];


$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_id = null;
if ($row = $result->fetch_assoc()) {
    $teacher_id = $row['teacher_id'];
}
$stmt->close();


$stmt = $conn->prepare("
    SELECT DISTINCT s.subject_id, s.subject_name
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE ts.teacher_id = ?
    ORDER BY s.subject_name
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_subjects = [];

while ($row = $result->fetch_assoc()) {
    $teacher_subjects[] = [
        'subject_id' => $row['subject_id'],
        'subject_name' => $row['subject_name'],
        'fees' => [] // Will be populated with fees for each grade
    ];
}
$stmt->close();


foreach ($teacher_subjects as $key => $subject) {
    $subject_id = $subject['subject_id'];

    $fees = [];
    foreach ($available_grades as $grade) {
        $fees[$grade] = 500.00; // Default fee
    }

    $fees_stmt = $conn->prepare("
        SELECT grade, fee
        FROM subject_fees
        WHERE subject_id = ?
    ");
    $fees_stmt->bind_param("i", $subject_id);
    $fees_stmt->execute();
    $fees_result = $fees_stmt->get_result();

    
    while ($fee_row = $fees_result->fetch_assoc()) {
        $fees[$fee_row['grade']] = $fee_row['fee'];
    }

    $fees_stmt->close();

   
    $teacher_subjects[$key]['fees'] = $fees;
}


if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_fees'])) {
    $subject_id = $_POST['subject_id'];
    $subject_name = '';

    
    foreach ($teacher_subjects as $ts) {
        if ($ts['subject_id'] == $subject_id) {
            $subject_name = $ts['subject_name'];
            break;
        }
    }

    $grades = $_POST['grade'];
    $fees = $_POST['fee'];
    $update_count = 0;
    $error_count = 0;

   
    foreach ($grades as $index => $grade) {
        $fee = $fees[$index];

     
        if (!is_numeric($fee) || $fee < 0) {
            $error_message = "Please enter valid fee amounts.";
            $error_count++;
            continue;
        }

        
        $check_stmt = $conn->prepare("SELECT id FROM subject_fees WHERE subject_id = ? AND grade = ?");
        $check_stmt->bind_param("is", $subject_id, $grade);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            // Update existing record
            $update_stmt = $conn->prepare("UPDATE subject_fees SET fee = ? WHERE subject_id = ? AND grade = ?");
            $update_stmt->bind_param("dis", $fee, $subject_id, $grade);

            if ($update_stmt->execute()) {
                $update_count++;
            } else {
                $error_count++;
            }
            $update_stmt->close();
        } else {
            
            $insert_stmt = $conn->prepare("INSERT INTO subject_fees (subject_id, grade, fee) VALUES (?, ?, ?)");
            $insert_stmt->bind_param("isd", $subject_id, $grade, $fee);

            if ($insert_stmt->execute()) {
                $update_count++;
            } else {
                $error_count++;
            }
            $insert_stmt->close();
        }

        $check_stmt->close();

       
        foreach ($teacher_subjects as $key => $subject_item) {
            if ($subject_item['subject_id'] == $subject_id) {
                $teacher_subjects[$key]['fees'][$grade] = $fee;
                break;
            }
        }
    }

    if ($error_count == 0 && $update_count > 0) {
        $success_message = "Fees updated successfully for " . htmlspecialchars($subject_name) . "!";
    } elseif ($error_count > 0) {
        $error_message = "Some fees could not be updated. Please try again.";
    }
}


$currentPage = basename($_SERVER['PHP_SELF']);
include_once '../../assets/teacher_sidebar.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Subject Fees</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
        }
        .content {
            margin-left: 260px;
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        @media (max-width: 768px) {
            .content {
                margin-left: 0;
                padding-top: 60px;
            }
        }

        @media (min-width: 769px) and (max-width: 992px) {
            .content {
                margin-left: 70px;
            }
        }

        .fee-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            margin-bottom: 20px;
        }

        .fee-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }

        /* Subject-specific styling */
        .subject-english .card-header {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
        }

        .subject-science .card-header {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        .subject-mathematics .card-header {
            background: linear-gradient(135deg, #f46b45 0%, #eea849 100%);
        }

        .subject-default .card-header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
        }
    </style>
</head>
<body>
    <div class="content">
        <div class="container-fluid">
            <div class="row mb-4">
                <div class="col-12">
                    <h1 class="mb-3"><i class="fas fa-money-bill-wave me-2"></i>Manage Subject Fees</h1>
                    <p class="text-muted">Set and update fees for subjects you teach</p>
                    <div class="d-flex justify-content-end">
                        <a href="view_all_subjects.php" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>View All Subjects
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?= $success_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?= $error_message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (empty($teacher_subjects)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> You are not assigned to teach any subjects yet.
                    <a href="add_subject.php" class="alert-link">Click here to add subjects</a>.
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($teacher_subjects as $subject): ?>
                        <div class="col-md-12 mb-4">
                            <?php
                                $subject_class = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $subject['subject_name']));

                                // Map subject names directly to their classes to avoid issues
                                $subject_class_map = [
                                    'English' => 'english',
                                    'Science' => 'science',
                                    'Mathematics' => 'mathematics',
                                ];

                                // Use the mapping if available, otherwise use the generated class
                                if (isset($subject_class_map[$subject['subject_name']])) {
                                    $subject_class = $subject_class_map[$subject['subject_name']];
                                } else {
                                    $valid_subjects = ['english', 'science', 'mathematics'];
                                    if (!in_array($subject_class, $valid_subjects)) {
                                        $subject_class = 'default';
                                    }
                                }
                            ?>
                            <div class="card fee-card subject-<?= $subject_class ?>">
                                <div class="card-header d-flex align-items-center">
                                    <div class="me-auto">
                                        <h4 class="mb-0">
                                            <?php if ($subject['subject_name'] === 'English'): ?>
                                                <i class="fas fa-language me-2"></i>
                                            <?php elseif ($subject['subject_name'] === 'Science'): ?>
                                                <i class="fas fa-flask me-2"></i>
                                            <?php elseif ($subject['subject_name'] === 'Mathematics'): ?>
                                                <i class="fas fa-calculator me-2"></i>
                                            <?php else: ?>
                                                <i class="fas fa-book me-2"></i>
                                            <?php endif; ?>
                                            <?= htmlspecialchars($subject['subject_name']) ?>
                                        </h4>
                                    </div>
                                    <div>
                                        <span class="badge bg-light text-primary fs-6">Subject ID: <?= $subject['subject_id'] ?></span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form action="manage_fees.php" method="POST">
                                        <input type="hidden" name="subject_id" value="<?= $subject['subject_id'] ?>">
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>Grade</th>
                                                        <th>Monthly Fee (LKR)</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($available_grades as $index => $grade): ?>
                                                        <tr>
                                                            <td>
                                                                <input type="hidden" name="grade[<?= $index ?>]" value="<?= htmlspecialchars($grade) ?>">
                                                                <span class="badge bg-primary"><?= htmlspecialchars($grade) ?></span>
                                                            </td>
                                                            <td>
                                                                <div class="input-group">
                                                                    <span class="input-group-text"><i class="fas fa-rupee-sign"></i></span>
                                                                    <input type="number" class="form-control"
                                                                           name="fee[<?= $index ?>]"
                                                                           value="<?= $subject['fees'][$grade] ?>"
                                                                           min="0" step="0.01" required>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>

                                        <div class="text-end mt-3">
                                            <button type="submit" name="update_fees" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i> Update All Fees
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
