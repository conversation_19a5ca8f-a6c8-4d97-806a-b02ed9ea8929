
// Global variables
let unreadCount = 0;
let notificationsData = [];
let notificationCheckInterval = null;

// Initialize notifications when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    
    if (document.getElementById('notificationBtn')) {
        initializeNotifications();

        
        console.log('Notifications system initialized');
    }
});


function initializeNotifications() {
    
    fetchNotifications();

    // Set up periodic checks 
    notificationCheckInterval = setInterval(fetchNotifications, 60000);

    // Add event listener to notification button
    document.getElementById('notificationBtn').addEventListener('click', toggleNotificationBox);

    
    const refreshBtn = document.getElementById('refreshNotifications');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function(event) {
            event.stopPropagation(); // Prevent closing the notification box
            fetchNotifications();

            
            const notificationList = document.getElementById('notificationList');
            notificationList.innerHTML = `
                <li class="list-group-item text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </li>
            `;
        });
    }

    // Add event listener to check reminders button
    const checkRemindersBtn = document.getElementById('checkRemindersBtn');
    if (checkRemindersBtn) {
        checkRemindersBtn.addEventListener('click', checkReminders);
    }

    // Close notification box when clicking outside
    document.addEventListener('click', function(event) {
        const notificationBox = document.getElementById('notificationBox');
        const notificationBtn = document.getElementById('notificationBtn');

        if (notificationBox && notificationBox.style.display === 'block') {
            if (!notificationBox.contains(event.target) && !notificationBtn.contains(event.target)) {
                notificationBox.style.display = 'none';
            }
        }
    });

    // Check for URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('reminder_check') && urlParams.get('reminder_check') === 'success') {
        showReminderCheckSuccess();
    }
}


function fetchNotifications(classRemindersOnly = true) {
    // Determine which endpoint to use based on the page and parameter
    const endpoint = classRemindersOnly
        ? '../../backend/notification/fetch_class_reminders.php'
        : '../../backend/notification/fetch_notifications.php';

    fetch(endpoint)
        .then(res => res.json())
        .then(data => {
            console.log('Notifications data:', data);

            notificationsData = data;
            unreadCount = data.filter(n => n.is_read === 0 || n.is_read === '0').length;

            updateNotificationUI(classRemindersOnly);

            // Show popup for important class reminders
            checkForClassReminders(data);
        })
        .catch(error => {
            console.error('Error fetching notifications:', error);
            // Show error in notification list if visible
            const notificationList = document.getElementById('notificationList');
            if (notificationList && document.getElementById('notificationBox').style.display === 'block') {
                notificationList.innerHTML = `
                    <li class="list-group-item text-center py-4">
                        <i class="fas fa-exclamation-circle text-danger mb-2" style="font-size: 2rem;"></i>
                        <p class="mb-0">Error loading notifications</p>
                        <small class="text-muted">Please try again later</small>
                    </li>
                `;
            }
        });
}


function updateNotificationUI(classRemindersOnly = true) {
    const notificationCount = document.getElementById('notificationCount');
    const notificationList = document.getElementById('notificationList');
    const notificationBoxHeader = document.querySelector('#notificationBox .card-header div:first-child');

    if (!notificationCount || !notificationList) return;

    // Update notification count badge
    notificationCount.textContent = unreadCount;
    notificationCount.style.display = unreadCount > 0 ? 'inline-block' : 'none';

    // Update header text based on what we're showing
    if (notificationBoxHeader) {
        if (classRemindersOnly) {
            notificationBoxHeader.innerHTML = '<i class="fas fa-calendar-alt me-2"></i><strong>Class Reminders</strong>';
        } else {
            notificationBoxHeader.innerHTML = '<i class="fas fa-bell me-2"></i><strong>Notifications</strong>';
        }
    }

    // Update notification list
    notificationList.innerHTML = '';

    if (notificationsData.length === 0) {
        notificationList.innerHTML = `
            <li class="list-group-item text-center py-4">
                <i class="fas fa-check-circle text-success mb-2" style="font-size: 2rem;"></i>
                <p class="mb-0">${classRemindersOnly ? 'No class reminders' : 'No notifications'}</p>
                ${classRemindersOnly ? '<small class="text-muted">Your upcoming classes will appear here</small>' : ''}
            </li>
        `;
        return;
    }

    // Add notifications to the list
    notificationsData.forEach(notification => {
        const li = document.createElement('li');
        li.className = 'list-group-item notification-item';

        // Check class reminder
        const isClassReminder = notification.message.includes('class') &&
                               (notification.message.includes('tomorrow') || notification.message.includes('today'));

        // Check system message
        const isSystemMessage = notification.is_system_message === true;

        // Format the time
        const notificationTime = new Date(notification.scheduled_time || notification.created_at);
        const timeAgo = formatTimeAgo(notificationTime);

        // Add special styling for class reminders
        if (isClassReminder) {
            li.classList.add('bg-light-warning');
        }

        // Add special styling for system messages
        if (isSystemMessage) {
            li.classList.add('bg-light-info');
        }

        // Extract grade and time information for better display
        let formattedMessage = notification.message;
        let gradeInfo = '';
        let timeInfo = '';

        // Try to extract grade information
        const gradeMatch = notification.message.match(/Grade\s+(\d+)/i);
        if (gradeMatch) {
            gradeInfo = `Grade ${gradeMatch[1]}`;
        }

        // Try to extract time information
        const timeMatch = notification.message.match(/at\s+(\d+:\d+\s*(?:AM|PM))/i);
        if (timeMatch) {
            timeInfo = timeMatch[1];
        }

        // Create the notification content
        if (isClassReminder && gradeInfo && timeInfo) {
            // For class reminders with grade and time info, create a more structured display
            li.innerHTML = `
                <div class="d-flex justify-content-between align-items-start mb-1">
                    <div class="notification-title fw-bold">
                        <i class="fas fa-calendar-alt text-warning me-2"></i>
                        ${gradeInfo}
                    </div>
                    <span class="badge bg-warning text-dark">${notification.message.includes('tomorrow') ? 'Tomorrow' : 'Today'}</span>
                </div>
                <div class="notification-details mb-1">
                    <i class="far fa-clock me-1"></i>${timeInfo}
                </div>
                <div class="notification-time text-muted small">
                    <i class="fas fa-history me-1"></i>${timeAgo}
                </div>
            `;
        } else {
            // For other notifications, use the standard format
            li.innerHTML = `
                <div class="notification-title ${isClassReminder || isSystemMessage ? 'fw-bold' : ''}">
                    <i class="fas ${isClassReminder ? 'fa-calendar-alt text-warning' : isSystemMessage ? 'fa-info-circle text-info' : 'fa-bell text-primary'} me-2"></i>
                    ${formattedMessage}
                </div>
                <div class="notification-time">
                    <i class="far fa-clock me-1"></i>${timeAgo}
                </div>
            `;
        }

        notificationList.appendChild(li);
    });
}


function toggleNotificationBox() {
    const notificationBox = document.getElementById('notificationBox');
    if (!notificationBox) return;

    if (notificationBox.style.display === 'none' || notificationBox.style.display === '') {
        notificationBox.style.display = 'block';

        // Mark notifications as read when opened
        if (unreadCount > 0) {
            fetch('../../backend/notification/mark_as_read.php')
                .then(res => res.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('notificationCount').style.display = 'none';
                        unreadCount = 0;
                    }
                })
                .catch(error => console.error('Error marking notifications as read:', error));
        }
    } else {
        notificationBox.style.display = 'none';
    }
}


function checkForClassReminders(notifications) {
    // Look for class reminders that contain "tomorrow"
    const classReminders = notifications.filter(notification =>
        notification.message.includes('class') &&
        notification.message.includes('tomorrow') &&
        notification.message.includes('IMPORTANT')
    );

    // Show popup for each important class reminder
    classReminders.forEach(reminder => {
        
        const reminderKey = `reminder_shown_${reminder.id}`;
        if (!localStorage.getItem(reminderKey)) {
            showReminderPopup(reminder);
            localStorage.setItem(reminderKey, 'true');
        }
    });
}


function showReminderPopup(reminder) {
    // Create popup element
    const popup = document.createElement('div');
    popup.className = 'reminder-popup';
    popup.innerHTML = `
        <div class="reminder-popup-content">
            <div class="reminder-header">
                <i class="fas fa-calendar-alt me-2"></i>
                <h4>Class Reminder</h4>
                <button class="reminder-close-btn">&times;</button>
            </div>
            <div class="reminder-body">
                <p>${reminder.message}</p>
                <p class="reminder-time">Please make sure you're prepared for this class.</p>
            </div>
            <div class="reminder-footer">
                <button class="reminder-dismiss-btn">Dismiss</button>
            </div>
        </div>
    `;

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
        .reminder-popup {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10001;
            max-width: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            animation: slideIn 0.3s ease-out;
        }
        .reminder-popup-content {
            padding: 0;
        }
        .reminder-header {
            display: flex;
            align-items: center;
            background: #ffc107;
            color: #000;
            padding: 12px 15px;
            border-radius: 8px 8px 0 0;
        }
        .reminder-header h4 {
            margin: 0 0 0 10px;
            flex-grow: 1;
            font-size: 18px;
        }
        .reminder-close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #000;
        }
        .reminder-body {
            padding: 15px;
        }
        .reminder-time {
            font-size: 14px;
            color: #666;
            margin-top: 10px;
        }
        .reminder-footer {
            padding: 10px 15px;
            text-align: right;
            border-top: 1px solid #eee;
        }
        .reminder-dismiss-btn {
            background: #ffc107;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;

    // Add to document
    document.head.appendChild(style);
    document.body.appendChild(popup);

    // Add event listeners
    popup.querySelector('.reminder-close-btn').addEventListener('click', () => {
        document.body.removeChild(popup);
    });

    popup.querySelector('.reminder-dismiss-btn').addEventListener('click', () => {
        document.body.removeChild(popup);
    });

    // Auto-remove after 30 seconds
    setTimeout(() => {
        if (document.body.contains(popup)) {
            document.body.removeChild(popup);
        }
    }, 30000);
}


function formatTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
        return 'just now';
    } else if (diffMin < 60) {
        return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
    } else if (diffHour < 24) {
        return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else if (diffDay < 7) {
        return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
    } else {
        return date.toLocaleDateString();
    }
}


function checkReminders() {
    // Get the button and result container
    const checkRemindersBtn = document.getElementById('checkRemindersBtn');
    const reminderCheckResult = document.getElementById('reminderCheckResult');

    if (!checkRemindersBtn || !reminderCheckResult) return;

    // Show loading state
    const originalButtonText = checkRemindersBtn.innerHTML;
    checkRemindersBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Checking...';
    checkRemindersBtn.disabled = true;

    // Show the result container with loading message
    reminderCheckResult.style.display = 'block';
    reminderCheckResult.innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-spinner fa-spin me-2"></i>Checking for reminders...
        </div>
    `;

    // Make AJAX request with XMLHttpRequest to set the X-Requested-With header
    const xhr = new XMLHttpRequest();
    xhr.open('GET', '../../backend/notification/check_reminders.php', true);
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
    xhr.onload = function() {
        // Reset button state
        checkRemindersBtn.innerHTML = originalButtonText;
        checkRemindersBtn.disabled = false;

        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);

                if (response.success) {
                    // Show success message
                    reminderCheckResult.innerHTML = `
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>${response.message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    `;

                    // Fetch notifications to update the list
                    fetchNotifications();

                    // Open the notification box to show the new notifications
                    const notificationBox = document.getElementById('notificationBox');
                    if (notificationBox) {
                        notificationBox.style.display = 'block';
                    }

                    // Auto-dismiss the alert after 5 seconds
                    setTimeout(() => {
                        const alert = reminderCheckResult.querySelector('.alert');
                        if (alert) {
                            const bsAlert = new bootstrap.Alert(alert);
                            bsAlert.close();
                        }
                    }, 5000);
                } else {
                    // Show error message
                    reminderCheckResult.innerHTML = `
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>Error: ${response.message || 'Unknown error'}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    `;
                }
            } catch (error) {
                // Show parsing error
                reminderCheckResult.innerHTML = `
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>Error parsing response: ${error.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
            }
        } else {
            // Show HTTP error
            reminderCheckResult.innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>HTTP Error: ${xhr.status}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
        }
    };

    xhr.onerror = function() {
        // Reset button state
        checkRemindersBtn.innerHTML = originalButtonText;
        checkRemindersBtn.disabled = false;

        // Show network error
        reminderCheckResult.innerHTML = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>Network error. Please try again.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
    };

    xhr.send();
}


function showReminderCheckSuccess() {
    const reminderCheckResult = document.getElementById('reminderCheckResult');
    if (!reminderCheckResult) return;

    // Show success message
    reminderCheckResult.style.display = 'block';
    reminderCheckResult.innerHTML = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>Reminders checked and sent.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    // Fetch notifications to update the list
    fetchNotifications();

    // Auto-dismiss the alert after 5 seconds
    setTimeout(() => {
        const alert = reminderCheckResult.querySelector('.alert');
        if (alert) {
            try {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            } catch (error) {
                // Fallback if bootstrap Alert is not available
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }
        }
    }, 5000);

    // Remove the parameter from the URL without reloading the page
    const url = new URL(window.location.href);
    url.searchParams.delete('reminder_check');
    window.history.replaceState({}, document.title, url.toString());
}
