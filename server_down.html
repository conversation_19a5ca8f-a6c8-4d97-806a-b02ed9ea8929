<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Down - Guru<PERSON>la LMS</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .background-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape-1 {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape-2 {
            width: 120px;
            height: 120px;
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape-3 {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape-4 {
            width: 100px;
            height: 100px;
            bottom: 10%;
            right: 20%;
            animation-delay: 1s;
        }

        .shape-5 {
            width: 140px;
            height: 140px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .error-container {
            position: relative;
            z-index: 2;
            max-width: 600px;
            width: 90%;
            margin: 0 auto;
        }

        .error-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: pulse 2s infinite;
        }

        .error-icon i {
            font-size: 3rem;
            color: white;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
            }
        }

        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .error-message {
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .status-info {
            background: rgba(52, 152, 219, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #3498db;
        }

        .status-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 500;
            color: #2c3e50;
        }

        .status-indicator {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .btn-retry {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin: 0.5rem;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn-retry:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        .auto-refresh {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(46, 204, 113, 0.1);
            border-radius: 10px;
            color: #27ae60;
            font-size: 0.9rem;
            border-left: 4px solid #27ae60;
        }

        .countdown {
            font-weight: 600;
            color: #e74c3c;
        }

        .instructions {
            margin-top: 2rem;
            padding: 1.5rem;
            background: rgba(241, 196, 15, 0.1);
            border-radius: 15px;
            border-left: 4px solid #f1c40f;
            text-align: left;
        }

        .instructions h5 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .instructions ol {
            margin: 0;
            padding-left: 1.5rem;
        }

        .instructions li {
            margin-bottom: 0.5rem;
            color: #7f8c8d;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .error-card {
                padding: 2rem 1.5rem;
            }

            .error-title {
                font-size: 2rem;
            }

            .error-icon {
                width: 100px;
                height: 100px;
            }

            .error-icon i {
                font-size: 2.5rem;
            }

            .btn-retry {
                display: block;
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="background-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
    </div>

    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-server"></i>
            </div>
            
            <h1 class="error-title">Server is Down</h1>
            <p class="error-message">
                We're sorry, but the Gurukula LMS server is currently unavailable. 
                Please try again later.
            </p>

            <div class="status-info">
                <div class="status-item">
                    <span class="status-label">
                        <i class="fas fa-times-circle" style="color: #e74c3c; margin-right: 8px;"></i>
                        XAMPP Server Status
                    </span>
                    <span class="status-indicator">
                        <i class="fas fa-times"></i> Offline
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">
                        <i class="fas fa-times-circle" style="color: #e74c3c; margin-right: 8px;"></i>
                        Web Server (Apache)
                    </span>
                    <span class="status-indicator">
                        <i class="fas fa-times"></i> Not Running
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">
                        <i class="fas fa-times-circle" style="color: #e74c3c; margin-right: 8px;"></i>
                        Database Server (MySQL)
                    </span>
                    <span class="status-indicator">
                        <i class="fas fa-times"></i> Not Running
                    </span>
                </div>
            </div>

            <div class="instructions">
                <h5><i class="fas fa-tools" style="margin-right: 8px;"></i>To fix this issue:</h5>
                <ol>
                    <li>Open XAMPP Control Panel</li>
                    <li>Start the Apache service</li>
                    <li>Start the MySQL service</li>
                    <li>Wait for both services to show "Running" status</li>
                    <li>Click "Try Again" below</li>
                </ol>
            </div>

            <button class="btn-retry" onclick="location.reload()">
                <i class="fas fa-redo-alt"></i>
                Try Again
            </button>

            <div class="auto-refresh">
                <i class="fas fa-sync-alt" style="margin-right: 8px;"></i>
                This page will automatically refresh in <span class="countdown" id="countdown">30</span> seconds
            </div>
        </div>
    </div>

    <script>
        // Countdown timer
        let timeLeft = 30;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            timeLeft--;
            countdownElement.textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(timer);
                location.reload();
            }
        }, 1000);

        // Try to detect when server comes back online
        function checkServerStatus() {
            fetch('/', { 
                method: 'HEAD',
                cache: 'no-cache',
                mode: 'no-cors'
            }).then(() => {
                // If this succeeds, server might be back
                location.reload();
            }).catch(() => {
                // Server still down, continue waiting
            });
        }

        // Check server status every 10 seconds
        setInterval(checkServerStatus, 10000);

        // Also check when the page becomes visible (user switches back to tab)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                checkServerStatus();
            }
        });
    </script>
</body>
</html>
