<?php 

include_once __DIR__ . '/../../backend/student/course_materials.php'; 
$currentPage = basename($_SERVER['PHP_SELF']);
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><?= htmlspecialchars($subject_name) ?> Materials</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --light-bg: #f8f9fa;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--light-bg);
      color: var(--dark-text);
    }
    
    .content {
      margin-left: 260px;
      padding: 30px;
    }
    
    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }
    
    .page-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }
    
    .material-card {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: var(--card-shadow);
      transition: all 0.3s ease;
      border-left: 5px solid var(--primary-color);
    }
    
    .material-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--hover-shadow);
    }
    
    .material-card h5 {
      color: var(--dark-text);
      font-weight: 600;
      margin-bottom: 1rem;
    }
    
    .material-card .download-link {
      display: inline-flex;
      align-items: center;
      color: var(--primary-color);
      font-weight: 500;
      text-decoration: none;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      background-color: rgba(67, 97, 238, 0.1);
      transition: all 0.3s ease;
    }
    
    .material-card .download-link:hover {
      background-color: rgba(67, 97, 238, 0.2);
    }
    
    .material-card .text-muted {
      color: var(--light-text) !important;
      font-size: 0.875rem;
    }
    
    .empty-state {
      text-align: center;
      padding: 4rem 2rem;
      background: white;
      border-radius: 16px;
      box-shadow: var(--card-shadow);
    }
    
    .empty-state i {
      font-size: 4rem;
      color: #d1d5db;
      margin-bottom: 1.5rem;
    }
    
    .empty-state p {
      color: var(--light-text);
      font-size: 1.25rem;
      margin-bottom: 0.5rem;
    }
    
    .back-button {
      display: inline-flex;
      align-items: center;
      background-color: white;
      color: var(--dark-text);
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 10px;
      font-weight: 600;
      text-decoration: none;
      box-shadow: var(--card-shadow);
      transition: all 0.3s ease;
      margin-bottom: 1.5rem;
    }
    
    .back-button:hover {
      transform: translateY(-3px);
      box-shadow: var(--hover-shadow);
      color: var(--primary-color);
    }
    
    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }
    }
  </style>
</head>
<body>

<?php include_once __DIR__ . '/../../assets/student_sidebar.php'; ?>

<div class="content">
  <a href="my_courses.php" class="back-button">
    <i class="fas fa-arrow-left me-2"></i> Back to My Courses
  </a>
  
  <div class="page-header">
    <h2 class="mb-1"><i class="fas fa-book-reader me-2"></i><?= htmlspecialchars($subject_name) ?></h2>
    <p class="mb-0">Course Materials for <?= htmlspecialchars($grade) ?> Grade</p>
  </div>

  <?php if (isset($materials) && $materials->num_rows > 0): ?>
    <div class="row">
      <?php while ($row = $materials->fetch_assoc()): ?>
        <div class="col-lg-6">
          <div class="material-card">
            <h5><?= htmlspecialchars($row['title']) ?></h5>
            <a href="../../<?= htmlspecialchars($row['file_path']) ?>" class="download-link mb-3" target="_blank">
              <i class="fas fa-download me-2"></i> Download Material
            </a>
            <p class="text-muted mt-3">
              <i class="far fa-calendar-alt me-1"></i> Uploaded on: <?= date("d M Y", strtotime($row['uploaded_at'])) ?>
            </p>
          </div>
        </div>
      <?php endwhile; ?>
    </div>
  <?php else: ?>
    <div class="empty-state">
      <i class="fas fa-file-alt"></i>
      <p>No materials uploaded yet for this subject.</p>
      <small>Check back later for updates from your teacher.</small>
    </div>
  <?php endif; ?>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
