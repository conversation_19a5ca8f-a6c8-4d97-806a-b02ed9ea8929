<?php include_once __DIR__ . '/../../backend/teacher/teacher_view_submissions.php'; ?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Assignment Submissions - <PERSON><PERSON><PERSON></title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    body {
      background: var(--light-bg);
      font-family: 'Poppins', sans-serif;
      color: var(--dark-text);
    }

    .content {
      margin-left: 280px;
      padding: 30px;
    }

    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }

    .card {
      background: white;
      border-radius: 16px;
      box-shadow: var(--card-shadow);
      border: none;
      transition: all 0.3s ease;
      margin-bottom: 30px;
      overflow: hidden;
    }

    .card:hover {
      box-shadow: var(--hover-shadow);
    }

    .card-header {
      padding: 1.25rem 1.5rem;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      font-weight: 600;
    }

    .card-body {
      padding: 1.5rem;
    }

    .table {
      margin-bottom: 0;
    }

    .table th {
      font-weight: 600;
      color: var(--dark-text);
      padding: 1rem;
      border-bottom-width: 1px;
    }

    .table td {
      padding: 1rem;
      vertical-align: middle;
    }

    .table tbody tr:hover {
      background-color: rgba(67, 97, 238, 0.05);
    }

    .form-control, .form-select {
      border-radius: 10px;
      padding: 0.6rem 1rem;
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
    }

    .form-control-sm {
      padding: 0.4rem 0.75rem;
      font-size: 0.875rem;
    }

    textarea.form-control-sm {
      min-height: 60px;
    }

    .btn {
      border-radius: 10px;
      padding: 0.5rem 1rem;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-sm {
      padding: 0.4rem 0.75rem;
      font-size: 0.875rem;
    }

    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }

    .btn-primary:hover {
      background-color: var(--secondary-color);
      border-color: var(--secondary-color);
      transform: translateY(-2px);
    }

    .btn-success {
      background-color: var(--success-color);
      border-color: var(--success-color);
    }

    .btn-success:hover {
      background-color: #27ae60;
      border-color: #27ae60;
      transform: translateY(-2px);
    }

    .btn-outline-primary {
      color: var(--primary-color);
      border-color: var(--primary-color);
    }

    .btn-outline-primary:hover {
      background-color: var(--primary-color);
      color: white;
      transform: translateY(-2px);
    }

    .alert {
      border-radius: 12px;
      padding: 1.25rem;
      margin-bottom: 1.5rem;
      border: none;
    }

    .alert-warning {
      background-color: rgba(243, 156, 18, 0.15);
      color: #9a6700;
    }

    .alert-info {
      background-color: rgba(72, 149, 239, 0.15);
      color: #2c5282;
    }

    .no-assignments {
      border-radius: 16px;
      box-shadow: var(--card-shadow);
      padding: 0;
      overflow: hidden;
    }

    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }
    }
  </style>
</head>
<body>

<?php include_once __DIR__ . '/../../assets/teacher_sidebar.php'; ?>

<div class="content">
  <div class="page-header">
    <h2 class="mb-2"><i class="fas fa-clipboard-check me-2"></i>Assignment Submissions</h2>
    <p class="mb-0">Review and grade student submissions for your assignments</p>
  </div>

  <?php if (!$has_assignments): ?>
    <div class="no-assignments">
      <div class="alert alert-warning m-0">
        <div class="d-flex align-items-center mb-3">
          <i class="fas fa-exclamation-triangle fs-4 me-3"></i>
          <h5 class="mb-0">No Assignments Found</h5>
        </div>
        <p>You haven't posted any assignments yet. Create an assignment to start receiving student submissions.</p>
        <div class="mt-3">
          <a href="../teacher/assignments.php" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i> Create Assignment
          </a>
        </div>
      </div>
    </div>
  <?php else: ?>
    <?php
    
    $assignments->data_seek(0);

   
    $has_any_submissions = false;

    while ($assignment = $assignments->fetch_assoc()):
      // Get submissions 
      $a_id = $assignment['assignment_id'];
      $stmt = $conn->prepare("SELECT s.*, CONCAT(u.first_name, ' ', u.last_name) AS student_name
                              FROM submissions s
                              JOIN students st ON s.student_id = st.student_id
                              JOIN users u ON st.user_id = u.id
                              WHERE s.assignment_id = ?");
      $stmt->bind_param("i", $a_id);
      $stmt->execute();
      $submissions = $stmt->get_result();

    
      if ($submissions->num_rows > 0) {
        $has_any_submissions = true;
      }
    ?>
      <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <div>
            <i class="fas fa-book me-2"></i>
            <strong><?= htmlspecialchars($assignment['title']) ?></strong>
            <span class="badge bg-info ms-2"><?= htmlspecialchars($assignment['subject_name']) ?></span>
          </div>
          <span class="badge bg-white text-primary"><?= htmlspecialchars($assignment['grade']) ?></span>
        </div>
        <div class="card-body p-0">
          <?php if ($submissions->num_rows > 0): ?>
            <div class="table-responsive">
              <table class="table align-middle">
                <thead class="table-light">
                  <tr>
                    <th>Student</th>
                    <th>File</th>
                    <th>Submitted</th>
                    <th style="width: 100px;">Marks</th>
                    <th style="width: 220px;">Feedback</th>
                    <th style="width: 100px;">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($sub = $submissions->fetch_assoc()): ?>
                    <tr>
                      <form method="POST" action="../../backend/teacher/update_submission.php">
                        <td>
                          <div class="d-flex align-items-center">
                            <div class="avatar-circle bg-primary text-white me-2">
                              <?= strtoupper(substr($sub['student_name'], 0, 1)) ?>
                            </div>
                            <div><?= htmlspecialchars($sub['student_name']) ?></div>
                          </div>
                        </td>
                        <td>
                          <a href="../../<?= $sub['file_path'] ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-file-download me-1"></i> View
                          </a>
                        </td>
                        <td>
                          <div><?= date("d M Y", strtotime($sub['submitted_at'])) ?></div>
                          <small class="text-muted"><?= date("h:i A", strtotime($sub['submitted_at'])) ?></small>
                        </td>
                        <td>
                          <input type="text" name="marks" value="<?= htmlspecialchars($sub['marks']) ?>" class="form-control form-control-sm" placeholder="Score">
                        </td>
                        <td>
                          <textarea name="comments" class="form-control form-control-sm" placeholder="Add feedback for student"><?= htmlspecialchars($sub['comments']) ?></textarea>
                        </td>
                        <td>
                          <input type="hidden" name="submission_id" value="<?= $sub['submission_id'] ?>">
                          <input type="hidden" name="subject_id" value="<?= $assignment['subject_id'] ?>">
                          <button type="submit" class="btn btn-sm btn-success w-100">
                            <i class="fas fa-save me-1"></i> Save
                          </button>
                        </td>
                      </form>
                    </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="text-center py-4">
              <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
              <p class="mb-1">No student submissions yet</p>
              <small class="text-muted">Students haven't submitted any work for this assignment</small>
            </div>
          <?php endif; ?>
        </div>
      </div>
    <?php endwhile; ?>

    <?php if (!$has_any_submissions): ?>
      <div class="alert alert-info d-flex align-items-center">
        <i class="fas fa-info-circle fs-4 me-3"></i>
        <div>
          <h5 class="mb-1">Waiting for Submissions</h5>
          <p class="mb-0">You have posted assignments, but no students have submitted their work yet.</p>
        </div>
      </div>
    <?php endif; ?>
  <?php endif; ?>
</div>

<style>

  .avatar-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
  }
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
