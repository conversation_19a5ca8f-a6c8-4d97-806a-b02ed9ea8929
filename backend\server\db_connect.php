<?php
$servername = "localhost";
$username = "root";
$password = "";
$database = "institution_db";

// Function to check if server is running
function isServerRunning() {
    // Check if MySQL service is running by attempting to connect
    $test_conn = @new mysqli("localhost", "root", "", "");
    if ($test_conn->connect_error) {
        return false;
    }
    $test_conn->close();
    return true;
}

// Function to check if database exists
function isDatabaseAvailable($servername, $username, $password, $database) {
    $test_conn = @new mysqli($servername, $username, $password, $database);
    if ($test_conn->connect_error) {
        return false;
    }
    $test_conn->close();
    return true;
}

// Check server status first
if (!isServerRunning()) {
    // Server is down - redirect to error page or show error message
    if (!headers_sent()) {
        header("Location: " . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . "://" . $_SERVER['HTTP_HOST'] . "/gurukula_lms/server_error.php");
        exit();
    } else {
        echo "<script>window.location.href = '/gurukula_lms/server_error.php';</script>";
        exit();
    }
}

// Check database availability
if (!isDatabaseAvailable($servername, $username, $password, $database)) {
    // Database is not available - redirect to error page
    if (!headers_sent()) {
        header("Location: " . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . "://" . $_SERVER['HTTP_HOST'] . "/gurukula_lms/database_error.php");
        exit();
    } else {
        echo "<script>window.location.href = '/gurukula_lms/database_error.php';</script>";
        exit();
    }
}

// If we reach here, server and database are available
$conn = new mysqli($servername, $username, $password, $database);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
?>
