<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Monitor Test - Gurukula LMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
        }
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-checking { background: #ffc107; animation: pulse 1s infinite; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .btn-test {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h1 class="text-center mb-4">
                <i class="fas fa-server me-2"></i>
                Server Monitor Test
            </h1>
            <p class="text-center text-muted mb-4">
                This page tests the server monitoring functionality. The monitor will check server status every minute.
            </p>

            <div class="row">
                <div class="col-md-6">
                    <h5>Current Status</h5>
                    <div id="status-display">
                        <div class="mb-2">
                            <span class="status-indicator status-checking" id="mysql-status"></span>
                            <span>MySQL Server: <span id="mysql-text">Checking...</span></span>
                        </div>
                        <div class="mb-2">
                            <span class="status-indicator status-checking" id="database-status"></span>
                            <span>Database: <span id="database-text">Checking...</span></span>
                        </div>
                        <div class="mb-2">
                            <span class="status-indicator status-ok" id="php-status"></span>
                            <span>PHP: <span id="php-text">Running</span></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Test Controls</h5>
                    <button class="btn btn-test me-2 mb-2" onclick="testHealthCheck()">
                        <i class="fas fa-heartbeat me-1"></i>
                        Check Health
                    </button>
                    <button class="btn btn-test me-2 mb-2" onclick="testNotification()">
                        <i class="fas fa-bell me-1"></i>
                        Test Notification
                    </button>
                    <button class="btn btn-test me-2 mb-2" onclick="simulateError()">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Simulate Error
                    </button>
                </div>
            </div>

            <div class="mt-4">
                <h5>Monitor Log</h5>
                <div id="log-display" style="background: #f8f9fa; padding: 1rem; border-radius: 8px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                    <div class="text-muted">Monitor log will appear here...</div>
                </div>
            </div>

            <div class="mt-4 text-center">
                <a href="frontend/home.php" class="btn btn-outline-primary">
                    <i class="fas fa-home me-1"></i>
                    Go to Home Page
                </a>
                <a href="server_error.php" class="btn btn-outline-danger ms-2">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    View Error Page
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/server_monitor.js"></script>
    <script>
        // Override the server monitor to capture logs
        const originalServerMonitor = window.serverMonitor;
        let logEntries = [];

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logEntries.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateLogDisplay();
        }

        function updateLogDisplay() {
            const logDisplay = document.getElementById('log-display');
            logDisplay.innerHTML = logEntries.map(entry => `<div>${entry}</div>`).join('');
            logDisplay.scrollTop = logDisplay.scrollHeight;
        }

        function updateStatus(mysql, database, php = true) {
            // Update MySQL status
            const mysqlStatus = document.getElementById('mysql-status');
            const mysqlText = document.getElementById('mysql-text');
            if (mysql) {
                mysqlStatus.className = 'status-indicator status-ok';
                mysqlText.textContent = 'Running';
            } else {
                mysqlStatus.className = 'status-indicator status-error';
                mysqlText.textContent = 'Down';
            }

            // Update Database status
            const databaseStatus = document.getElementById('database-status');
            const databaseText = document.getElementById('database-text');
            if (database) {
                databaseStatus.className = 'status-indicator status-ok';
                databaseText.textContent = 'Available';
            } else {
                databaseStatus.className = 'status-indicator status-error';
                databaseText.textContent = 'Not Found';
            }
        }

        async function testHealthCheck() {
            addLog('Manual health check initiated');
            try {
                const response = await fetch('/gurukula_lms/backend/server/health_check.php?debug=1');
                const data = await response.json();
                
                addLog(`Health check response: ${data.status}`);
                addLog(`MySQL: ${data.checks.mysql_server ? 'OK' : 'FAIL'}`);
                addLog(`Database: ${data.checks.database ? 'OK' : 'FAIL'}`);
                
                updateStatus(data.checks.mysql_server, data.checks.database);
                
                if (data.status === 'ok') {
                    addLog('All systems operational', 'success');
                } else {
                    addLog(`Error: ${data.message}`, 'error');
                }
            } catch (error) {
                addLog(`Health check failed: ${error.message}`, 'error');
                updateStatus(false, false);
            }
        }

        function testNotification() {
            if (window.serverMonitor) {
                window.serverMonitor.showNotification('This is a test notification!', 'info');
                addLog('Test notification displayed');
            } else {
                addLog('Server monitor not available', 'error');
            }
        }

        function simulateError() {
            if (window.serverMonitor) {
                window.serverMonitor.handleServerError('Simulated error for testing');
                addLog('Simulated server error', 'warning');
            } else {
                addLog('Server monitor not available', 'error');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addLog('Server monitor test page loaded');
            
            // Run initial health check
            setTimeout(testHealthCheck, 1000);
            
            // Override server monitor methods to capture logs
            if (window.serverMonitor) {
                const originalHandleResponse = window.serverMonitor.handleServerResponse;
                const originalHandleError = window.serverMonitor.handleServerError;
                
                window.serverMonitor.handleServerResponse = function(data) {
                    addLog(`Server response: ${data.status}`, 'success');
                    updateStatus(data.checks?.mysql_server, data.checks?.database);
                    return originalHandleResponse.call(this, data);
                };
                
                window.serverMonitor.handleServerError = function(error) {
                    addLog(`Server error: ${error}`, 'error');
                    updateStatus(false, false);
                    return originalHandleError.call(this, error);
                };
                
                addLog('Server monitor initialized and hooked');
            }
        });
    </script>
</body>
</html>
