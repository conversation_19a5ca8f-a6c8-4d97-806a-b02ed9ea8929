<?php

include_once '../../backend/server/db_connect.php';


header('Content-Type: application/json');


$current_month = date('m');
$current_year = date('Y');


$response = [
    'week1' => 8,
    'week2' => 12,
    'week3' => 15,
    'week4' => 10,
    'week5' => 6
];


$query = "
    SELECT
        week,
        COUNT(DISTINCT student_id) as student_count
    FROM
        attendance
    WHERE
        MONTH(date) = ? AND YEAR(date) = ?
    GROUP BY
        week
";

$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $current_month, $current_year);
$stmt->execute();
$result = $stmt->get_result();

// Process results
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
       
        if (preg_match('/Week\s+(\d+)/i', $row['week'], $matches)) {
            $week_num = (int)$matches[1];
            $key = 'week' . $week_num;
            if (isset($response[$key])) {
                $response[$key] = (int)$row['student_count'];
            }
        } else {
           
            $week = strtolower($row['week']);
            if ($week == 'week 1' || $week == 'week1') {
                $response['week1'] = (int)$row['student_count'];
            } else if ($week == 'week 2' || $week == 'week2') {
                $response['week2'] = (int)$row['student_count'];
            } else if ($week == 'week 3' || $week == 'week3') {
                $response['week3'] = (int)$row['student_count'];
            } else if ($week == 'week 4' || $week == 'week4') {
                $response['week4'] = (int)$row['student_count'];
            } else if ($week == 'week 5' || $week == 'week5') {
                $response['week5'] = (int)$row['student_count'];
            }
        }
    }
}

// Return JSON response
echo json_encode($response);

$stmt->close();
$conn->close();
