<?php
/**
 * Health Check Endpoint
 * Returns JSON status of server and database connectivity
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Allow AJAX requests
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
    // This is an AJAX request, proceed
} else {
    // For non-AJAX requests, you might want to restrict access
    // Uncomment the next line if you want to restrict to AJAX only
    // http_response_code(403); exit(json_encode(['status' => 'error', 'message' => 'Access denied']));
}

$response = [
    'status' => 'error',
    'message' => 'Unknown error',
    'timestamp' => date('Y-m-d H:i:s'),
    'checks' => [
        'mysql_server' => false,
        'database' => false,
        'php' => true // If we reach here, <PERSON><PERSON> is working
    ]
];

try {
    // Check MySQL server connection
    $mysql_conn = @new mysqli("localhost", "root", "");
    if ($mysql_conn->connect_error) {
        $response['message'] = 'MySQL server is not running';
        $response['checks']['mysql_server'] = false;
    } else {
        $response['checks']['mysql_server'] = true;
        $mysql_conn->close();
        
        // Check database connection
        $db_conn = @new mysqli("localhost", "root", "", "institution_db");
        if ($db_conn->connect_error) {
            $response['message'] = 'Database "institution_db" is not available';
            $response['checks']['database'] = false;
        } else {
            $response['checks']['database'] = true;
            $db_conn->close();
            
            // If we reach here, everything is working
            $response['status'] = 'ok';
            $response['message'] = 'All systems operational';
        }
    }
} catch (Exception $e) {
    $response['message'] = 'Health check failed: ' . $e->getMessage();
}

// Add server information for debugging (optional)
if (isset($_GET['debug']) && $_GET['debug'] === '1') {
    $response['debug'] = [
        'php_version' => phpversion(),
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown'
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
