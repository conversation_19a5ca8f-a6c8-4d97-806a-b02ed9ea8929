<?php
session_start();
include_once '../server/db_connect.php';


if (!isset($_SESSION['user_id'])) {
    echo '<div class="alert alert-danger">Unauthorized access</div>';
    exit();
}


$student_id = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;
$subject_id = isset($_GET['subject_id']) ? intval($_GET['subject_id']) : 0;

if (!$student_id || !$subject_id) {
    echo '<div class="alert alert-danger">Missing required parameters</div>';
    exit();
}

try {
   
    $student_query = $conn->prepare("
        SELECT s.student_id, CONCAT(u.first_name, ' ', u.last_name) AS student_name, s.grade
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE s.student_id = ?
    ");
    $student_query->bind_param("i", $student_id);
    $student_query->execute();
    $student_result = $student_query->get_result();

    if ($student_result->num_rows === 0) {
        echo '<div class="alert alert-danger">Student not found</div>';
        exit();
    }

    $student = $student_result->fetch_assoc();

    
    $subject_query = $conn->prepare("SELECT subject_name FROM subjects WHERE subject_id = ?");
    $subject_query->bind_param("i", $subject_id);
    $subject_query->execute();
    $subject_result = $subject_query->get_result();

    if ($subject_result->num_rows === 0) {
        echo '<div class="alert alert-danger">Subject not found</div>';
        exit();
    }

    $subject = $subject_result->fetch_assoc();

    // Get payment history
    $payment_query = $conn->prepare("
        SELECT
            pr.payment_id,
            pr.payment_period,
            pr.payment_date,
            pr.payment_method,
            pr.amount,
            pr.status,
            pr.cancelled_date,
            'System' AS processed_by
        FROM payment_records pr
        WHERE pr.student_id = ? AND pr.subject_id = ?
        ORDER BY pr.payment_period DESC, pr.payment_date DESC
    ");
    $payment_query->bind_param("ii", $student_id, $subject_id);
    $payment_query->execute();
    $payment_result = $payment_query->get_result();

   
    echo '<div class="mb-4">';
    echo '<h5>Student: <span class="text-primary">' . htmlspecialchars($student['student_name']) . '</span></h5>';
    echo '<p>Grade: <span class="badge bg-info">' . htmlspecialchars($student['grade']) . '</span></p>';
    echo '<p>Subject: <span class="badge bg-secondary">' . htmlspecialchars($subject['subject_name']) . '</span></p>';
    echo '</div>';

  
    if ($payment_result->num_rows > 0) {
        echo '<div class="table-responsive">';
        echo '<table class="table table-bordered table-striped">';
        echo '<thead class="table-primary">';
        echo '<tr>';
        echo '<th>Payment Period</th>';
        echo '<th>Date</th>';
        echo '<th>Method</th>';
        echo '<th>Amount</th>';
        echo '<th>Status</th>';
        echo '<th>Processed By</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        while ($payment = $payment_result->fetch_assoc()) {
            $status_class = $payment['status'] === 'PAID' ? 'success' : 'danger';

            echo '<tr>';
            echo '<td>' . date('F Y', strtotime($payment['payment_period'] . '-01')) . '</td>';
            echo '<td>' . date('d M Y', strtotime($payment['payment_date'])) . '</td>';
            echo '<td>' . htmlspecialchars($payment['payment_method']) . '</td>';
            echo '<td>$' . number_format($payment['amount'], 2) . '</td>';
            echo '<td><span class="badge bg-' . $status_class . '">' . htmlspecialchars($payment['status']) . '</span></td>';
            echo '<td>' . htmlspecialchars($payment['processed_by'] ?? 'System') . '</td>';
            echo '</tr>';

            if ($payment['status'] === 'CANCELLED' && $payment['cancelled_date']) {
                echo '<tr class="table-warning">';
                echo '<td colspan="6" class="text-muted"><i class="fas fa-info-circle me-2"></i> This payment was cancelled on ' . date('d M Y', strtotime($payment['cancelled_date'])) . '</td>';
                echo '</tr>';
            }
        }

        echo '</tbody>';
        echo '</table>';
        echo '</div>';
    } else {
        echo '<div class="alert alert-info">';
        echo '<i class="fas fa-info-circle me-2"></i> No payment history found for this student and subject.';
        echo '</div>';
    }
} catch (Exception $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-triangle me-2"></i> Error: ' . htmlspecialchars($e->getMessage());
    echo '</div>';
}

$conn->close();
?>