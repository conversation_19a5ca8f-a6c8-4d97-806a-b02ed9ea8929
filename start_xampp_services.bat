@echo off
echo Starting XAMPP services with proper permissions...

REM Run as administrator
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
if '%errorlevel%' NEQ '0' (
    echo Requesting administrative privileges...
    goto UACPrompt
) else ( goto gotAdmin )

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
    "%temp%\getadmin.vbs"
    exit /B

:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"

REM Stop any running services first
echo Stopping any running XAMPP services...
taskkill /F /IM httpd.exe /T > nul 2>&1
taskkill /F /IM mysqld.exe /T > nul 2>&1

REM Fix permissions before starting
echo Fixing permissions...
icacls "C:\xampp\mysql\data" /grant Everyone:(OI)(CI)F /T
icacls "C:\xampp\mysql\data\*" /grant Everyone:F /T
icacls "C:\xampp\apache\logs" /grant Everyone:(OI)(CI)F /T
icacls "C:\xampp\tmp" /grant Everyone:(OI)(CI)F /T

REM Start Apache
echo Starting Apache...
start "" /B "C:\xampp\apache_start.bat"

REM Wait a moment
timeout /t 2 > nul

REM Start MySQL
echo Starting MySQL...
start "" /B "C:\xampp\mysql_start.bat"

echo.
echo XAMPP services should now be running.
echo If you still have issues, run fix_permissions.bat and try again.
echo.
echo Press any key to exit...
pause > nul
