<?php
session_start();

$currentPage = basename($_SERVER['PHP_SELF']);

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'worker') {
    header("Location: ../../index.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QR Attendance Scanner</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://unpkg.com/html5-qrcode"></script>
  <style>
    :root {
      --primary-color: #1e3c72;
      --secondary-color: #2a5298;
      --accent-color: #67BAFD;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-color: #f8f9fa;
      --dark-color: #343a40;
      --border-radius: 15px;
      --card-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      --transition-speed: 0.3s;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f4f7fe;
      margin: 0;
      padding: 0;
    }

    .content {
      margin-left: 280px;
      padding: 30px;
      transition: all var(--transition-speed) ease;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .page-title {
      font-size: 1.8rem;
      font-weight: 600;
      color: var(--primary-color);
      margin: 0;
      display: flex;
      align-items: center;
    }

    .page-title i {
      margin-right: 12px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10px;
      font-size: 1.2rem;
    }

    .scanner-card {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      padding: 25px;
      transition: all var(--transition-speed) ease;
      height: 100%;
      border: none;
      overflow: hidden;
      position: relative;
    }

    #reader {
      width: 100%;
      max-width: 500px;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .history-card {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      height: 100%;
      border: none;
      overflow: hidden;
    }

    .history-card .card-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      font-weight: 600;
      padding: 15px 20px;
      border: none;
      display: flex;
      align-items: center;
    }

    .history-card .card-header i {
      margin-right: 10px;
    }

    .history-card .card-body {
      padding: 20px;
      max-height: 400px;
      overflow-y: auto;
    }

    .scan-item {
      background: white;
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
      transition: all var(--transition-speed) ease;
      border-left: 4px solid var(--accent-color);
    }

    .scan-item:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .scan-item .student-name {
      font-weight: 600;
      font-size: 1.1rem;
      margin-bottom: 5px;
      color: var(--primary-color);
    }

    .scan-item .scan-time {
      color: #6c757d;
      font-size: 0.85rem;
    }

    .scan-item .scan-details {
      display: flex;
      gap: 8px;
      margin-top: 8px;
    }

    .scan-item .badge {
      padding: 6px 10px;
      font-weight: 500;
    }

    .scan-success {
      background-color: rgba(46, 204, 113, 0.15);
      border-color: var(--success-color);
    }

    .scan-error {
      background-color: rgba(231, 76, 60, 0.15);
      border-color: var(--danger-color);
    }

    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }

      .page-title {
        font-size: 1.5rem;
      }
    }
  </style>
</head>
<body>

<?php include_once '../../assets/worker_sidebar.php'; ?>

<div class="content">
  <div class="page-header">
    <h1 class="page-title">
      <i class="fas fa-qrcode"></i>
      <span>QR Attendance Scanner</span>
    </h1>
  </div>

  <div class="row g-4">
    <div class="col-lg-6">
      <div class="scanner-card">
        <h5 class="mb-4"><i class="fas fa-camera me-2"></i> Scan QR Code</h5>
        <div id="reader" class="mb-4"></div>
        <div id="scanResult" class="mt-3"></div>
      </div>
    </div>
    <div class="col-lg-6">
      <div class="history-card">
        <div class="card-header">
          <i class="fas fa-history"></i>
          <span>Recent Scans</span>
        </div>
        <div class="card-body">
          <div id="scanHistory"></div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const html5QrCode = new Html5Qrcode("reader");
  const scanResult = document.getElementById('scanResult');
  const scanHistory = document.getElementById('scanHistory');

  // Configure scanner with modern styling
  const qrboxFunction = function(viewfinderWidth, viewfinderHeight) {
    const minEdgePercentage = 0.7; 
    const minEdgeSize = Math.min(viewfinderWidth, viewfinderHeight);
    const qrboxSize = Math.floor(minEdgeSize * minEdgePercentage);
    return {
      width: qrboxSize,
      height: qrboxSize
    };
  };

  const qrCodeSuccessCallback = async (decodedText, decodedResult) => {
   
    const successSound = new Audio('../../assets/sounds/beep.mp3');
    try {
      successSound.play();
    } catch (e) {
      console.log('Sound could not be played');
    }

    
    html5QrCode.stop();

    // Show loading indicator
    scanResult.innerHTML = `
      <div class="alert alert-info d-flex align-items-center">
        <div class="spinner-border spinner-border-sm me-3" role="status"></div>
        <div>
          <strong>Processing...</strong><br>
          <small class="text-muted">Decoded QR data: ${decodedText.substring(0, 30)}${decodedText.length > 30 ? '...' : ''}</small>
        </div>
      </div>
    `;

    try {
    
      const response = await fetch('../../backend/attendance/mark_attendance.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ qrData: decodedText })
      });

      
      const rawResponse = await response.text();
      console.log('Raw response:', rawResponse);

      
      let data;
      try {
        data = JSON.parse(rawResponse);
      } catch (e) {
        throw new Error(`Invalid JSON response: ${rawResponse}`);
      }

      const currentTime = new Date();
      const formattedTime = currentTime.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });

      if (data.success) {
        // Show success message
        scanResult.innerHTML = `
          <div class="alert alert-success border-0 shadow-sm">
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-check-circle me-2" style="font-size: 1.5rem; color: var(--success-color);"></i>
              <h5 class="mb-0">Attendance Marked Successfully!</h5>
            </div>
            <div class="row mt-3">
              <div class="col-md-6">
                <p class="mb-1"><strong>Student:</strong> ${data.student_name}</p>
                <p class="mb-1"><strong>ID:</strong> ${data.student_id}</p>
              </div>
              <div class="col-md-6">
                <p class="mb-1"><strong>Grade:</strong> ${data.grade}</p>
                <p class="mb-1"><strong>Subject:</strong> ${data.subject}</p>
                <p class="mb-1"><strong>Week:</strong> ${data.week}</p>
              </div>
            </div>
          </div>
        `;

        // Add to history 
        const historyItem = `
          <div class="scan-item scan-success">
            <div class="d-flex justify-content-between align-items-start">
              <div class="student-name">${data.student_name}</div>
              <div class="scan-time">${formattedTime}</div>
            </div>
            <div class="scan-details">
              <span class="badge bg-primary">ID: ${data.student_id}</span>
              <span class="badge bg-info">Grade: ${data.grade}</span>
              <span class="badge bg-secondary">Week: ${data.week}</span>
            </div>
          </div>
        `;
        scanHistory.innerHTML = historyItem + scanHistory.innerHTML;

        
        try {
          //update the counter in the parent window 
          if (window.opener && !window.opener.closed) {
            try {
              if (window.opener.location.href.includes('worker_dashboard.php')) {
                if (typeof window.opener.updateTodayAttendance === 'function') {
                  window.opener.updateTodayAttendance();
                }
              }
            } catch (e) {
              console.log('Could not access opener window:', e);
            }
          }

          //  update any worker dashboard 
          localStorage.setItem('attendance_updated', Date.now().toString());
        } catch (e) {
          console.log('Could not update attendance counter:', e);
        }
      } else {
        
        scanResult.innerHTML = `
          <div class="alert alert-danger border-0 shadow-sm">
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-exclamation-circle me-2" style="font-size: 1.5rem; color: var(--danger-color);"></i>
              <h5 class="mb-0">Error</h5>
            </div>
            <p class="mb-0">${data.message}</p>
          </div>
        `;

        const historyItem = `
          <div class="scan-item scan-error">
            <div class="d-flex justify-content-between align-items-start">
              <div class="student-name">Scan Error</div>
              <div class="scan-time">${formattedTime}</div>
            </div>
            <p class="mb-1">${data.message}</p>
          </div>
        `;
        scanHistory.innerHTML = historyItem + scanHistory.innerHTML;
      }
    } catch (error) {
      
      scanResult.innerHTML = `
        <div class="alert alert-danger border-0 shadow-sm">
          <div class="d-flex align-items-center mb-2">
            <i class="fas fa-exclamation-triangle me-2" style="font-size: 1.5rem; color: var(--danger-color);"></i>
            <h5 class="mb-0">System Error</h5>
          </div>
          <p class="mb-0">Failed to process QR code.</p>
          <small class="text-muted">${error.message}</small>
        </div>
      `;
      console.error('Error details:', error);

     
      const historyItem = `
        <div class="scan-item scan-error">
          <div class="d-flex justify-content-between align-items-start">
            <div class="student-name">System Error</div>
            <div class="scan-time">${new Date().toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })}</div>
          </div>
          <p class="mb-1">Failed to process QR code</p>
        </div>
      `;
      scanHistory.innerHTML = historyItem + scanHistory.innerHTML;
    }

    // Restart scanning after 5 seconds
    setTimeout(() => {
      startScanner();
    }, 5000);
  };

  function startScanner() {
    html5QrCode.start(
      { facingMode: "environment" },
      { fps: 10, qrbox: qrboxFunction },
      qrCodeSuccessCallback,
      (errorMessage) => {
        
        console.log(errorMessage);
      }
    ).catch((err) => {
      console.error("Failed to start scanner", err);
      scanResult.innerHTML = `
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <span>Could not start camera. Please check camera permissions.</span>
        </div>
      `;
    });
  }

  
  startScanner();
});
</script>
</body>
</html>


