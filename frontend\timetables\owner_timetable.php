<?php
session_start();

$currentPage = basename($_SERVER['PHP_SELF']);

// Check if user is logged in and is an owner
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'owner') {
    header("Location: ../../backend/server/login.php");
    exit();
}

include_once '../../assets/owner_sidebar.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <title>All Teacher Timetables</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #1cc88a;
      --info-color: #36b9cc;
      --warning-color: #f6c23e;
      --danger-color: #e74a3b;
      --light-color: #f8f9fc;
      --dark-color: #5a5c69;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    body {
      background-color: var(--light-color);
      font-family: 'Poppins', sans-serif;
    }
    
    .content {
      margin-left: 260px;
      padding: 30px;
      transition: all 0.3s;
    }
    
    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }
    
    .page-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }
    
    .page-header h2 {
      color: white;
      font-weight: 700;
      margin-bottom: 0.5rem;
      position: relative;
      z-index: 1;
    }
    
    .page-header p {
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 0;
      position: relative;
      z-index: 1;
    }
    
    .filters-card {
      background-color: white;
      border-radius: 0.75rem;
      padding: 1.5rem 2rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.1);
    }
    
    .filters-card h5 {
      color: var(--primary-color);
      font-weight: 700;
      margin-bottom: 1.25rem;
    }
    
    .table-card {
      background-color: white;
      border-radius: 0.75rem;
      padding: 1.5rem 2rem;
      box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.1);
    }
    
    .form-select, .form-control {
      border-radius: 0.5rem;
      padding: 0.5rem 1rem;
      border: 1px solid #d1d3e2;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-select:focus, .form-control:focus {
      border-color: #bac8f3;
      box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }
    
    .btn {
      border-radius: 0.5rem;
      padding: 0.5rem 1.25rem;
      font-weight: 600;
      transition: all 0.2s;
    }
    
    .btn:hover {
      transform: translateY(-2px);
    }
    
    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }
    
    .btn-primary:hover {
      background-color: #2e59d9;
      border-color: #2653d4;
    }
    
    .btn-danger {
      background-color: var(--danger-color);
      border-color: var(--danger-color);
    }
    
    .btn-danger:hover {
      background-color: #be2617;
      border-color: #bd2130;
    }
    
    .loading-spinner {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 3rem;
    }
    
    .error-message {
      text-align: center;
      padding: 2rem;
      color: var(--danger-color);
      background-color: rgba(231, 76, 60, 0.1);
      border-radius: 10px;
      margin: 2rem 0;
    }
    
    table.dataTable {
      border-collapse: collapse !important;
      border-radius: 0.5rem;
      overflow: hidden;
    }
    
    table.dataTable thead th {
      background-color: var(--light-color);
      color: var(--dark-color);
      font-weight: 700;
      border-bottom: 1px solid #e3e6f0;
      padding: 1rem;
    }
    
    table.dataTable tbody td {
      padding: 1rem;
      vertical-align: middle;
      border-bottom: 1px solid #e3e6f0;
    }
    
    table.dataTable tbody tr:hover {
      background-color: rgba(78, 115, 223, 0.05);
    }
    
    .dataTables_wrapper .dataTables_length, 
    .dataTables_wrapper .dataTables_filter, 
    .dataTables_wrapper .dataTables_info, 
    .dataTables_wrapper .dataTables_processing, 
    .dataTables_wrapper .dataTables_paginate {
      color: var(--dark-color);
      margin: 1rem 0;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
      background: var(--primary-color);
      color: white !important;
      border: 1px solid var(--primary-color);
      border-radius: 0.35rem;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:not(.current):hover {
      background: var(--light-color);
      color: var(--primary-color) !important;
      border: 1px solid var(--primary-color);
    }
    
    .badge-subject, .badge-grade, .badge-day {
      display: inline-block;
      padding: 0.35rem 0.65rem;
      font-size: 0.75rem;
      font-weight: 700;
      border-radius: 0.35rem;
    }
    
    .badge-subject {
      background-color: rgba(78, 115, 223, 0.1);
      color: var(--primary-color);
    }
    
    .badge-grade {
      background-color: rgba(54, 185, 204, 0.1);
      color: var(--info-color);
    }
    
    .badge-day {
      background-color: rgba(246, 194, 62, 0.1);
      color: var(--warning-color);
    }
    
    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }
      
      .page-header, .filters-card, .table-card {
        padding: 1rem;
      }
      
      .filters .col-md-3 {
        margin-bottom: 1rem;
      }
    }
  </style>
</head>
<body>
<div class="content">
  <div class="page-header">
    <h2><i class="fas fa-calendar-alt me-2"></i>All Teacher Timetables</h2>
    <p>View and filter all class schedules across teachers and subjects</p>
  </div>

  <div class="filters-card">
    <h5><i class="fas fa-filter me-2"></i>Filter Options</h5>
    <div class="row filters align-items-end">
      <div class="col-md-3">
        <label for="subjectFilter" class="form-label">Subject</label>
        <select id="subjectFilter" class="form-select">
          <option value="">All Subjects</option>
        </select>
      </div>
      <div class="col-md-3">
        <label for="gradeFilter" class="form-label">Grade</label>
        <select id="gradeFilter" class="form-select">
          <option value="">All Grades</option>
        </select>
      </div>
      <div class="col-md-3">
        <label for="teacherFilter" class="form-label">Teacher</label>
        <select id="teacherFilter" class="form-select">
          <option value="">All Teachers</option>
        </select>
      </div>
      <div class="col-md-3 text-end">
        <button id="clearFiltersBtn" class="btn btn-danger">
          <i class="fas fa-undo me-2"></i>Clear Filters
        </button>
      </div>
    </div>
  </div>

  <div class="table-card">
    <div id="loadingSpinner" class="loading-spinner">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <div id="errorMessage" class="error-message" style="display: none;">
      <i class="fas fa-exclamation-circle fa-2x mb-3"></i>
      <h5>Failed to load timetables</h5>
      <p>Please try refreshing the page or contact support if the problem persists.</p>
    </div>

    <div class="table-responsive">
      <table id="timetableTable" class="table table-hover">
        <thead>
          <tr>
            <th>Day</th>
            <th>Grade</th>
            <th>Subject</th>
            <th>Teacher Name</th>
            <th>Time</th>
          </tr>
        </thead>
        <tbody>
          
        </tbody>
      </table>
    </div>
  </div>
</div>


<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>

<script>
  let table;

  async function loadAllTimetables() {
    try {
      // Show loading spinner
      document.getElementById('loadingSpinner').style.display = 'flex';
      document.getElementById('errorMessage').style.display = 'none';

      const res = await fetch('../../backend/timetables/get_all_timetables.php');

      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`);
      }

      const data = await res.json();
      console.log('Fetched Data:', data);

      // Hide loading spinner
      document.getElementById('loadingSpinner').style.display = 'none';

      if (!data || !data.length) {
        document.querySelector('#timetableTable tbody').innerHTML = "<tr><td colspan='5' class='text-center'>No timetables found.</td></tr>";
        return;
      }

      let rows = '';
      const subjects = new Set();
      const grades = new Set();
      const teachers = new Set();

      data.forEach(t => {
        rows += `<tr>
                   <td><span class="badge-day">${t.day}</span></td>
                   <td><span class="badge-grade">Grade ${t.grade}</span></td>
                   <td><span class="badge-subject">${t.subject}</span></td>
                   <td>${t.teacher_name}</td>
                   <td><i class="far fa-clock me-1"></i>${t.time}</td>
                 </tr>`;

        subjects.add(t.subject);
        grades.add(t.grade);
        teachers.add(t.teacher_name);
      });

      document.querySelector('#timetableTable tbody').innerHTML = rows;

      // Initialize DataTable
      if ($.fn.DataTable.isDataTable('#timetableTable')) {
        table.destroy();
      }

      table = $('#timetableTable').DataTable({
        "order": [[0, 'asc'], [4, 'asc']],
        "pageLength": 10,
        "language": {
          "emptyTable": "No timetables found",
          "zeroRecords": "No matching records found",
          "info": "Showing _START_ to _END_ of _TOTAL_ entries",
          "infoEmpty": "Showing 0 to 0 of 0 entries",
          "infoFiltered": "(filtered from _MAX_ total entries)",
          "search": "<i class='fas fa-search'></i> Search:",
          "paginate": {
            "first": "<i class='fas fa-angle-double-left'></i>",
            "last": "<i class='fas fa-angle-double-right'></i>",
            "next": "<i class='fas fa-angle-right'></i>",
            "previous": "<i class='fas fa-angle-left'></i>"
          }
        },
        "dom": '<"top"lf>rt<"bottom"ip>',
        "responsive": true
      });

      // Populate filter dropdowns
      const subjectFilter = document.getElementById('subjectFilter');
      const gradeFilter = document.getElementById('gradeFilter');
      const teacherFilter = document.getElementById('teacherFilter');

      // Clear existing options (except the first one)
      subjectFilter.innerHTML = '<option value="">All Subjects</option>';
      gradeFilter.innerHTML = '<option value="">All Grades</option>';
      teacherFilter.innerHTML = '<option value="">All Teachers</option>';

      // Add new options
      subjects.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject;
        option.textContent = subject;
        subjectFilter.appendChild(option);
      });

      grades.forEach(grade => {
        const option = document.createElement('option');
        option.value = grade;
        option.textContent = `Grade ${grade}`;
        gradeFilter.appendChild(option);
      });

      teachers.forEach(teacher => {
        const option = document.createElement('option');
        option.value = teacher;
        option.textContent = teacher;
        teacherFilter.appendChild(option);
      });

    } catch (error) {
      console.error('Error loading timetables:', error);
      document.getElementById('loadingSpinner').style.display = 'none';
      document.getElementById('errorMessage').style.display = 'block';
      document.getElementById('errorMessage').textContent = 'Failed to load timetables. Please try again later.';
      document.querySelector('#timetableTable tbody').innerHTML = "<tr><td colspan='5' class='text-center'>Failed to load timetables.</td></tr>";
    }
  }

  function applyFilters() {
    const subject = $('#subjectFilter').val();
    const grade = $('#gradeFilter').val();
    const teacher = $('#teacherFilter').val();

    table.column(2).search(subject, true, false)
         .column(1).search(grade, true, false)
         .column(3).search(teacher, true, false)
         .draw();
  }

  function clearFilters() {
    $('#subjectFilter').val('');
    $('#gradeFilter').val('');
    $('#teacherFilter').val('');

    table.columns().search('').draw();
  }

  $(document).ready(function() {
    loadAllTimetables();

    $('#subjectFilter, #gradeFilter, #teacherFilter').on('change', function() {
      applyFilters();
    });

    $('#clearFiltersBtn').on('click', function() {
      clearFilters();
    });
  });
</script>

</body>
</html>
