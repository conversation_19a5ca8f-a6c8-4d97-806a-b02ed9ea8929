<?php include_once __DIR__ . '/../../backend/student/my_courses.php'; ?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>My Courses - Gurukula</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--light-bg);
      color: var(--dark-text);
    }
    
    .content {
      margin-left: 280px;
      padding: 30px;
    }
    .course-card {
      background: #fff;
      border-radius: 10px;
      padding: 25px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      transition: 0.3s;
    }
    .course-card:hover {
      transform: translateY(-4px);
    }
  </style>
</head>
<body>

<?php include_once __DIR__ . '/../../assets/student_sidebar.php'; ?>

<div class="content">
  <h2 class="mb-4">📚 My Courses</h2>

  <div class="row">
    <?php if (empty($subjects)): ?>
      <div class="col-12">
        <div class="alert alert-info">
          <i class="fas fa-info-circle me-2"></i> You haven't registered for any subjects yet.
          <div class="mt-3">
            <a href="add_subject.php" class="btn btn-primary btn-sm">
              <i class="fas fa-plus me-1"></i> Add Subject
            </a>
          </div>
        </div>
      </div>
    <?php else: ?>
      <?php foreach ($subjects as $subject): ?>
        <div class="col-md-6 mb-4">
          <a href="course_materials.php?subject_id=<?= $subject['subject_id'] ?>&grade=<?= urlencode("Grade $grade") ?>" style="text-decoration: none;">
            <div class="course-card p-4">
              <h5><?= htmlspecialchars($subject['subject_name']) ?></h5>
              <p class="text-muted">Grade <?= htmlspecialchars($grade) ?></p>
            </div>
          </a>
        </div>
      <?php endforeach; ?>
    <?php endif; ?>
  </div>
</div>
</body>
</html>
