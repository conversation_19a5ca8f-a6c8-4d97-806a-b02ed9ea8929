<?php
include_once 'backend/server/db_connect.php';

echo "<h1>Clean Up Teacher Subjects Table</h1>";

// Get all teacher_subjects entries
$result = $conn->query("SELECT * FROM teacher_subjects ORDER BY teacher_id, subject_id");

echo "<h2>Current teacher_subjects Data</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Teacher ID</th><th>Subject ID</th></tr>";
$teacher_subjects = [];
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['teacher_id'] . "</td>";
    echo "<td>" . $row['subject_id'] . "</td>";
    echo "</tr>";
    
    // Store for later processing
    $teacher_subjects[] = $row;
}
echo "</table>";

// Find duplicate entries (same teacher_id and subject_id)
$duplicates = [];
$unique_entries = [];
$unique_keys = [];

foreach ($teacher_subjects as $entry) {
    $key = $entry['teacher_id'] . '-' . $entry['subject_id'];
    
    if (in_array($key, $unique_keys)) {
        $duplicates[] = $entry;
    } else {
        $unique_keys[] = $key;
        $unique_entries[] = $entry;
    }
}

echo "<h2>Duplicate Entries Found</h2>";
if (count($duplicates) > 0) {
    echo "<p>Found " . count($duplicates) . " duplicate entries to remove.</p>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Teacher ID</th><th>Subject ID</th><th>Action</th></tr>";
    
    foreach ($duplicates as $dup) {
        echo "<tr>";
        echo "<td>" . $dup['id'] . "</td>";
        echo "<td>" . $dup['teacher_id'] . "</td>";
        echo "<td>" . $dup['subject_id'] . "</td>";
        echo "<td><a href='?delete=" . $dup['id'] . "'>Delete</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Option to delete all duplicates at once
    echo "<p><a href='?delete_all=1'>Delete All Duplicates</a></p>";
} else {
    echo "<p>No duplicate entries found. The teacher_subjects table is clean.</p>";
}

// Handle deletion
if (isset($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    $stmt = $conn->prepare("DELETE FROM teacher_subjects WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        echo "<div style='background-color: #d4edda; color: #155724; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "Successfully deleted entry with ID: " . $id;
        echo "</div>";
    } else {
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "Error deleting entry: " . $stmt->error;
        echo "</div>";
    }
    $stmt->close();
}

// Handle deletion of all duplicates
if (isset($_GET['delete_all']) && $_GET['delete_all'] == 1 && count($duplicates) > 0) {
    $deleted_count = 0;
    $error_count = 0;
    
    foreach ($duplicates as $dup) {
        $stmt = $conn->prepare("DELETE FROM teacher_subjects WHERE id = ?");
        $stmt->bind_param("i", $dup['id']);
        
        if ($stmt->execute()) {
            $deleted_count++;
        } else {
            $error_count++;
        }
        $stmt->close();
    }
    
    if ($error_count == 0) {
        echo "<div style='background-color: #d4edda; color: #155724; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "Successfully deleted all " . $deleted_count . " duplicate entries.";
        echo "</div>";
    } else {
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "Deleted " . $deleted_count . " entries, but encountered errors with " . $error_count . " entries.";
        echo "</div>";
    }
}

// Add links to navigate
echo "<p><a href='cleanup_teacher_subjects.php'>Refresh This Page</a></p>";
echo "<p><a href='frontend/teacher/manage_fees.php'>Go to Manage Fees Page</a></p>";

$conn->close();
?>
