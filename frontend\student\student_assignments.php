<?php include_once __DIR__ . '/../../backend/student/student_assignments.php'; ?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>My Assignments</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--light-bg);
      color: var(--dark-text);
    }
    
    .content {
      margin-left: 280px;
      padding: 30px;
    }
    
    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }
    
    .page-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }
    
    .assignment-card {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: var(--card-shadow);
      transition: all 0.3s ease;
      border-left: 5px solid var(--primary-color);
      position: relative;
      overflow: hidden;
    }
    
    .assignment-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--hover-shadow);
    }
    
    .assignment-card h5 {
      color: var(--dark-text);
      font-weight: 600;
      margin-bottom: 1rem;
    }
    
    .assignment-card .description {
      color: var(--light-text);
      margin-bottom: 1.5rem;
      line-height: 1.6;
    }
    
    .assignment-card .meta-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
      font-size: 0.9rem;
    }
    
    .assignment-card .meta-info .item {
      display: flex;
      align-items: center;
    }
    
    .assignment-card .meta-info .item i {
      margin-right: 0.5rem;
      color: var(--primary-color);
    }
    
    .status-badge {
      padding: 0.35rem 0.75rem;
      border-radius: 50px;
      font-size: 0.8rem;
      font-weight: 600;
      display: inline-flex;
      align-items: center;
    }
    
    .status-badge.submitted {
      background-color: rgba(46, 204, 113, 0.15);
      color: var(--success-color);
    }
    
    .status-badge.pending {
      background-color: rgba(231, 76, 60, 0.15);
      color: var(--danger-color);
    }
    
    .status-badge i {
      margin-right: 0.4rem;
    }
    
    .download-link {
      display: inline-flex;
      align-items: center;
      color: var(--primary-color);
      font-weight: 500;
      text-decoration: none;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      background-color: rgba(67, 97, 238, 0.1);
      transition: all 0.3s ease;
      margin-bottom: 1rem;
    }
    
    .download-link:hover {
      background-color: rgba(67, 97, 238, 0.2);
      color: var(--primary-color);
    }
    
    .submission-form {
      background-color: rgba(67, 97, 238, 0.05);
      border-radius: 12px;
      padding: 1.5rem;
      margin-top: 1.5rem;
    }
    
    .submission-form label {
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: var(--dark-text);
    }
    
    .submission-form .form-control {
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      padding: 0.75rem 1rem;
    }
    
    .submission-form .form-control:focus {
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
      border-color: var(--primary-color);
    }
    
    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      border-radius: 8px;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
      background-color: var(--secondary-color);
      border-color: var(--secondary-color);
      transform: translateY(-2px);
    }
    
    .submission-info {
      background-color: rgba(67, 97, 238, 0.05);
      border-radius: 12px;
      padding: 1.5rem;
      margin-top: 1.5rem;
    }
    
    .submission-info p {
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }
    
    .submission-info p i {
      margin-right: 0.5rem;
      color: var(--primary-color);
    }
    
    .alert-info {
      background-color: rgba(72, 149, 239, 0.15);
      border: none;
      color: var(--accent-color);
      border-radius: 12px;
      padding: 1.5rem;
      display: flex;
      align-items: center;
    }
    
    .alert-info i {
      font-size: 1.5rem;
      margin-right: 1rem;
    }
    
    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }
      
      .page-header {
        padding: 1.5rem;
      }
      
      .assignment-card {
        padding: 1.25rem;
      }
      
      .meta-info {
        flex-direction: column;
        gap: 0.75rem;
      }
    }
  </style>
</head>
<body>

<?php include_once __DIR__ . '/../../assets/student_sidebar.php'; ?>

<div class="content">
  <div class="page-header">
    <h2 class="mb-1"><i class="fas fa-tasks me-2"></i>My Assignments</h2>
    <p class="mb-0">View and submit your assignments</p>
  </div>

  <?php if ($assignments->num_rows === 0): ?>
    <div class="alert alert-info">
      <i class="fas fa-info-circle"></i>
      <span>No assignments available for your grade right now.</span>
    </div>
  <?php endif; ?>

  <?php while ($row = $assignments->fetch_assoc()): ?>
    <?php
      
      $a_id = $row['assignment_id'];
      $check = $conn->prepare("SELECT * FROM submissions WHERE assignment_id = ? AND student_id = ?");
      $check->bind_param("ii", $a_id, $student_id);
      $check->execute();
      $submitted = $check->get_result()->fetch_assoc();
      $check->close();
      
      // Calculate days remaining
      $deadline = new DateTime($row['deadline']);
      $today = new DateTime();
      $interval = $today->diff($deadline);
      $daysRemaining = $deadline > $today ? $interval->days : -$interval->days;
    ?>
    
    <div class="assignment-card">
      <h5><?= htmlspecialchars($row['title']) ?></h5>
      <div class="description"><?= nl2br(htmlspecialchars($row['description'])) ?></div>
      
      <div class="meta-info">
        <div class="item">
          <i class="far fa-calendar-alt"></i>
          <span>Deadline: <?= date("d M Y", strtotime($row['deadline'])) ?></span>
        </div>
        
        <div class="item">
          <i class="far fa-clock"></i>
          <span>
            <?php if ($daysRemaining > 0): ?>
              <?= $daysRemaining ?> days remaining
            <?php elseif ($daysRemaining == 0): ?>
              Due today
            <?php else: ?>
              <span class="text-danger">Overdue by <?= abs($daysRemaining) ?> days</span>
            <?php endif; ?>
          </span>
        </div>
        
        <div class="item">
          <i class="fas fa-check-circle"></i>
          <span>Status: 
            <?php if ($submitted): ?>
              <span class="status-badge submitted"><i class="fas fa-check"></i>Submitted</span>
            <?php else: ?>
              <span class="status-badge pending"><i class="fas fa-hourglass-half"></i>Pending</span>
            <?php endif; ?>
          </span>
        </div>
      </div>
      
      <?php if ($row['file_path']): ?>
        <a href="../../<?= htmlspecialchars($row['file_path']) ?>" class="download-link" target="_blank">
          <i class="fas fa-download me-2"></i> Download Assignment File
        </a>
      <?php endif; ?>

      <?php if (!$submitted): ?>
        <div class="submission-form">
          <h6 class="mb-3"><i class="fas fa-upload me-2"></i>Submit Your Work</h6>
          <form action="" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="assignment_id" value="<?= $row['assignment_id'] ?>">
            <div class="mb-3">
              <label for="submission_file_<?= $row['assignment_id'] ?>" class="form-label">Upload Your Work (PDF/DOCX)</label>
              <input type="file" name="submission_file" id="submission_file_<?= $row['assignment_id'] ?>" class="form-control" required>
            </div>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-paper-plane me-2"></i>Submit Assignment
            </button>
          </form>
        </div>
      <?php else: ?>
        <div class="submission-info">
          <h6 class="mb-3"><i class="fas fa-check-circle me-2"></i>Submission Details</h6>
          <p><i class="fas fa-file-alt"></i>File: <a href="../../<?= htmlspecialchars($submitted['file_path']) ?>" target="_blank">View Submitted File</a></p>
          <p><i class="far fa-clock"></i>Submitted on: <?= date("d M Y, h:i A", strtotime($submitted['submitted_at'])) ?></p>
        </div>
      <?php endif; ?>
    </div>
  <?php endwhile; ?>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
