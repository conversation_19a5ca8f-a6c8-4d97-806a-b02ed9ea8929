<?php
session_start();
include '../../backend/server/db_connect.php';


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];


$stmt = $conn->prepare("
    SELECT u.first_name, u.last_name, u.email, t.phone, t.profile_image,
           GROUP_CONCAT(s.subject_name SEPARATOR ', ') as subject_names
    FROM users u
    JOIN teachers t ON u.id = t.user_id
    LEFT JOIN teacher_subjects ts ON t.teacher_id = ts.teacher_id
    LEFT JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE u.id = ?
    GROUP BY u.id, t.teacher_id
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$stmt->close();
$conn->close();


$teacher['user_name'] = $teacher['first_name'] . ' ' . $teacher['last_name'];


$imagePath = isset($teacher['profile_image']) && $teacher['profile_image']
    ? $teacher['profile_image']
    : 'assets/default.jpg';
?>
