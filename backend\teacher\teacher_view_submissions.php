<?php
session_start();
include __DIR__ . '/../server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../server/login.php");
    exit();
}

$teacher_id = $_SESSION['user_id'];


$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher_data = $result->fetch_assoc();
$teacher_id = $teacher_data['teacher_id']; 
$stmt->close();

// Get the subjects the teacher teaches
$subjects = [];
$stmt = $conn->prepare("
    SELECT s.subject_id, s.subject_name
    FROM subjects s
    JOIN teacher_subjects ts ON s.subject_id = ts.subject_id
    WHERE ts.teacher_id = ?
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$subjects_result = $stmt->get_result();
while ($row = $subjects_result->fetch_assoc()) {
    $subjects[] = $row;
}
$stmt->close();

// Get all assignments by this teacher with subject information
$stmt = $conn->prepare("
    SELECT a.*, s.subject_name
    FROM assignments a
    JOIN subjects s ON a.subject_id = s.subject_id
    WHERE a.teacher_id = ?
    ORDER BY a.posted_at DESC
");
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$assignments = $stmt->get_result();
$stmt->close();


$has_assignments = ($assignments->num_rows > 0);
?>
