<?php
// Fix the include path based on how the script is called
$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';

// This script provides instructions for setting up a cron job to run check_reminders.php

// First, make sure the email_logs table exists
include_once 'setup_email_logs.php';

// Display instructions for setting up a cron job
echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Cron Job Setup Instructions</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { padding: 20px; }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1 class='mb-4'>Cron Job Setup Instructions</h1>

        <div class='alert alert-info'>
            <p>To set up automatic class reminders, you need to configure a cron job on your server to run the check_reminders.php script periodically.</p>
        </div>

        <h3>For Linux/Unix servers:</h3>
        <p>Add the following line to your crontab (run <code>crontab -e</code>):</p>
        <div class='code-block'>
            # Run every hour to check for class reminders<br>
            0 * * * * php " . realpath(dirname(__FILE__)) . "/check_reminders.php
        </div>

        <h3>For Windows servers:</h3>
        <p>We've created a batch file for you. You can download it here:</p>
        <div class='mb-3'>
            <a href='run_reminders.bat' download class='btn btn-primary'>
                <i class='fas fa-download me-2'></i>Download Batch File
            </a>
        </div>

        <p>The batch file contains:</p>
        <div class='code-block'>
            @echo off<br>
            \"C:\\xampp\\php\\php.exe\" \"" . str_replace('/', '\\', realpath(dirname(__FILE__))) . "\\check_reminders.php\"
        </div>

        <p>Then set up a scheduled task to run this batch file hourly:</p>
        <ol>
            <li>Open Task Scheduler (search for it in the Start menu)</li>
            <li>Click on \"Create Basic Task\" in the right panel</li>
            <li>Enter a name like \"Gurukula Class Reminders\" and click Next</li>
            <li>Select \"Daily\" and click Next</li>
            <li>Set the start time and click Next</li>
            <li>Select \"Start a program\" and click Next</li>
            <li>Browse to select the downloaded batch file and click Next</li>
            <li>Check \"Open the Properties dialog\" and click Finish</li>
            <li>In the Properties dialog, go to the Triggers tab</li>
            <li>Edit the daily trigger and change it to \"Repeat task every 1 hour\"</li>
            <li>Click OK to save the task</li>
        </ol>

        <h3>Manual Testing:</h3>
        <p>You can manually test the reminder system by clicking the button below:</p>
        <form action='check_reminders.php' method='get'>
            <button type='submit' class='btn btn-primary'>Run Reminder Check Now</button>
        </form>

        <hr>

        <h3>Current Configuration:</h3>
        <p><strong>Script location:</strong> " . realpath(dirname(__FILE__)) . "/check_reminders.php</p>
        <p><strong>PHP executable:</strong> C:\\xampp\\php\\php.exe</p>

        <div class='mt-4'>
            <a href='../../frontend/dashboards/teacher_dashboard.php' class='btn btn-secondary'>Back to Dashboard</a>
        </div>
    </div>
</body>
</html>";
?>
