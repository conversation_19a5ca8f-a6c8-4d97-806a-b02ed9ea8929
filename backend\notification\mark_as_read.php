<?php
session_start();
// Fix the include path based on how the script is called
$base_path = __DIR__ . '/../../';
include_once $base_path . 'backend/server/db_connect.php';

// Add debugging
error_log("Marking notifications as read");

$user_id = $_SESSION['user_id'] ?? 0;
$role = $_SESSION['role'] ?? '';

error_log("User ID: $user_id, Role: $role");

if ($role !== 'teacher') {
    error_log("Not a teacher, returning error");
    echo json_encode(['success' => false, 'message' => 'Not authorized']);
    exit();
}

// Get teacher ID
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$res = $stmt->get_result();
$teacher = $res->fetch_assoc();
$teacher_id = $teacher['teacher_id'] ?? 0;

error_log("Teacher ID: $teacher_id");

if ($teacher_id) {
    // Mark all notifications as read for this teacher
    $q = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE teacher_id = ? AND is_read = 0");
    $q->bind_param("i", $teacher_id);
    $result = $q->execute();

    $affected_rows = $conn->affected_rows;
    error_log("Marked $affected_rows notifications as read");

    if ($result) {
        echo json_encode(['success' => true, 'message' => 'Notifications marked as read', 'count' => $affected_rows]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to mark notifications as read']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Teacher not found']);
}
?>
