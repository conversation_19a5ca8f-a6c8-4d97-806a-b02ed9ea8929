<?php
session_start();
include '../server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header("Location: ../server/login.php");
    exit();
}

if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['submission_id'])) {
    $submission_id = $_POST['submission_id'];
    $marks = $_POST['marks'] ?? '';
    $comments = $_POST['comments'] ?? '';

    // Validate submission belongs to an assignment created by this teacher
    $user_id = $_SESSION['user_id'];

    $stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $teacher = $result->fetch_assoc();
    $teacher_id = $teacher['teacher_id'];
    $stmt->close();

   
    $check = $conn->prepare("
        SELECT s.submission_id, a.assignment_id, a.subject_id, a.teacher_id
        FROM submissions s
        JOIN assignments a ON s.assignment_id = a.assignment_id
        WHERE s.submission_id = ? AND a.teacher_id = ?
    ");
    $check->bind_param("ii", $submission_id, $teacher_id);
    $check->execute();
    $check_result = $check->get_result();

    // Get the assignment details
    $assignment_data = $check_result->fetch_assoc();

    if ($check_result->num_rows === 0) {
        $_SESSION['error'] = "You don't have permission to update this submission.";
        header("Location: ../../frontend/teacher/teacher_view_submissions.php");
        exit();
    }
    $check->close();

    // Update the submission
    $update = $conn->prepare("UPDATE submissions SET marks = ?, comments = ? WHERE submission_id = ?");
    $update->bind_param("ssi", $marks, $comments, $submission_id);

    if ($update->execute()) {
        $_SESSION['success'] = "Submission updated successfully.";
    } else {
        $_SESSION['error'] = "Error updating submission: " . $update->error;
    }
    $update->close();

    
    header("Location: ../../frontend/teacher/teacher_view_submissions.php");
    exit();
} else {
    header("Location: ../../frontend/teacher/teacher_view_submissions.php");
    exit();
}
?>
