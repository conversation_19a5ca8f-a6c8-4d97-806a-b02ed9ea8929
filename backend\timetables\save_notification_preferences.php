<?php
session_start();
include_once '../../backend/server/db_connect.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Check if user is logged in as student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get student_id
    $stmt = $conn->prepare("SELECT student_id FROM students WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception("No student record found for this user");
    }

    $student = $result->fetch_assoc();
    $student_id = $student['student_id'];

    // Get form data
    $email_notifications = isset($_POST['email_notifications']) ? '1' : '0';
    $browser_notifications = isset($_POST['browser_notifications']) ? '1' : '0';
    $reminder_timing = $_POST['reminder_timing'] ?? '1';
    $browser_permission = $_POST['browser_permission'] ?? 'default';

    // Validate reminder timing
    if (!in_array($reminder_timing, ['0', '1', '2', '3'])) {
        $reminder_timing = '1';
    }

    // Update student notification preferences
    $stmt = $conn->prepare("
        UPDATE students
        SET email_notifications = ?,
            popup_notifications = ?
        WHERE student_id = ?
    ");
    $stmt->bind_param("ssi",
        $email_notifications,
        $browser_notifications,
        $student_id
    );

    if ($stmt->execute()) {
        // Log the notification preferences update
        $log_stmt = $conn->prepare("
            INSERT INTO notification_logs
            (student_id, notification_type, subject, message, status)
            VALUES (?, 'browser', 'Notification Preferences Updated', ?, 'sent')
        ");

        $log_message = "Email notifications: " . ($email_notifications == '1' ? 'Enabled' : 'Disabled') .
                      ", Browser notifications: " . ($browser_notifications == '1' ? 'Enabled' : 'Disabled') .
                      ", Reminder timing: " . $reminder_timing . " day(s) before";

        $log_stmt->bind_param("is", $student_id, $log_message);
        $log_stmt->execute();

        echo json_encode([
            'success' => true,
            'message' => 'Notification preferences saved successfully',
            'preferences' => [
                'email_notifications' => $email_notifications,
                'browser_notifications' => $browser_notifications,
                'reminder_timing' => $reminder_timing
            ]
        ]);
    } else {
        throw new Exception("Failed to save notification preferences: " . $stmt->error);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

$conn->close();
?>
