<?php
// Set headers to ensure only <PERSON><PERSON><PERSON> is returned
header('Content-Type: application/json');

// Disable error display in the output
ini_set('display_errors', 0);
error_reporting(E_ALL);


session_start();
require_once '../server/db_connect.php';


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Not authorized']);
    exit;
}

// Check if required fields are present
if (!isset($_POST['id']) || !isset($_POST['reply'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

$id = $_POST['id'];
$reply = $_POST['reply'];
$teacher_id = $_SESSION['user_id'];

// Validate inputs
if (empty($id) || empty($reply)) {
    echo json_encode(['success' => false, 'message' => 'Empty required fields']);
    exit;
}

// Get the teacher_id from the teachers table
try {
    $stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    $stmt->bind_param("i", $teacher_id);
    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }

    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Teacher record not found']);
        exit;
    }

    $teacher_data = $result->fetch_assoc();
    $teacher_id = $teacher_data['teacher_id']; // actual teacher_id
} catch (Exception $e) {
    error_log("Error in reply_question.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred. Please try again later.']);
    exit;
}

// Check if the question exists and belongs to the teacher's subject
try {
    $checkStmt = $conn->prepare("
        SELECT q.id
        FROM questions q
        JOIN teacher_subjects ts ON q.subject_id = ts.subject_id
        WHERE q.id = ?
        AND ts.teacher_id = ?
    ");

    if (!$checkStmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    $checkStmt->bind_param("ii", $id, $teacher_id);

    if (!$checkStmt->execute()) {
        throw new Exception("Execute failed: " . $checkStmt->error);
    }

    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Question not found or not authorized to reply']);
        exit;
    }

    $checkStmt->close();
} catch (Exception $e) {
    error_log("Error checking question authorization: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error verifying question. Please try again later.']);
    exit;
}

// Update the question with the reply
try {
    $stmt = $conn->prepare("UPDATE questions SET reply_text = ?, replied_at = NOW(), replied_by = ?, is_new_reply = 1, is_notified = 0 WHERE id = ?");

    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    $stmt->bind_param("sii", $reply, $teacher_id, $id);

    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }

    // Check if any rows were affected
    if ($stmt->affected_rows > 0) {
        // Get student_id and subject_id for the question
        $getQuestionInfo = $conn->prepare("
            SELECT q.student_id, q.subject_id, s.subject_name
            FROM questions q
            JOIN subjects s ON q.subject_id = s.subject_id
            WHERE q.id = ?
        ");
        $getQuestionInfo->bind_param("i", $id);
        $getQuestionInfo->execute();
        $questionInfo = $getQuestionInfo->get_result()->fetch_assoc();

        if ($questionInfo) {
            $student_id = $questionInfo['student_id'];
            $subject_name = $questionInfo['subject_name'];

            // Check if student_notifications table exists
            $tableExists = false;
            $result = $conn->query("SHOW TABLES LIKE 'student_notifications'");
            if ($result->num_rows > 0) {
                $tableExists = true;
            }

            if (!$tableExists) {
                
                $sql = "CREATE TABLE IF NOT EXISTS student_notifications (
                    id INT(11) AUTO_INCREMENT PRIMARY KEY,
                    student_id INT(11) NOT NULL,
                    message VARCHAR(255) NOT NULL,
                    is_read TINYINT(1) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    question_id INT(11) NULL
                )";
                $conn->query($sql);
            }

            // Create notification for student
            $message = "Your question in {$subject_name} has been answered";
            $insertNotification = $conn->prepare("
                INSERT INTO student_notifications (student_id, message, created_at, question_id)
                VALUES (?, ?, NOW(), ?)
            ");
            $insertNotification->bind_param("isi", $student_id, $message, $id);
            $insertNotification->execute();
        }

        echo json_encode(['success' => true, 'message' => 'Reply submitted successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'No changes were made. The question may have been deleted.']);
    }

    $stmt->close();
} catch (Exception $e) {
    error_log("Error updating question with reply: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred. Please try again later.'
    ]);
} finally {
   
    $conn->close();
}
?>