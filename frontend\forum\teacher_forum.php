<?php
session_start();
include_once '../../assets/teacher_sidebar.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <title>Teacher Forum</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --secondary-color: #3f37c9;
      --accent-color: #4895ef;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f4f6f9;
      --dark-text: #2d3748;
      --light-text: #718096;
      --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    body {
      margin: 0;
      padding: 0;
      background-color: var(--light-bg);
      font-family: 'Poppins', sans-serif;
      color: var(--dark-text);
    }

    .content {
      margin-left: 260px;
      padding: 30px;
    }

    .page-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 200%;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(30deg);
    }

    .filter-bar {
      background: white;
      border-radius: 12px;
      padding: 1.25rem;
      margin-bottom: 1.5rem;
      box-shadow: var(--card-shadow);
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
      gap: 1rem;
    }

    .filter-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
      align-items: center;
    }

    .form-select {
      border-radius: 10px;
      border: 1px solid #e2e8f0;
      padding: 0.5rem 2.5rem 0.5rem 1rem;
      font-size: 0.9rem;
      background-color: #f8f9fa;
      transition: all 0.3s ease;
    }

    .form-select:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
    }

    .form-check-input {
      width: 1.1em;
      height: 1.1em;
      margin-top: 0.2em;
      cursor: pointer;
    }

    .form-check-input:checked {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }

    .form-check-label {
      cursor: pointer;
      font-size: 0.95rem;
    }

    .question-card {
      background: white;
      border-radius: 12px;
      box-shadow: var(--card-shadow);
      margin-bottom: 1.5rem;
      border-left: 4px solid #6c757d;
      transition: all 0.3s ease;
      overflow: hidden;
    }

    .question-card:hover {
      box-shadow: var(--hover-shadow);
      transform: translateY(-2px);
    }

    .question-card.answered {
      border-left-color: var(--success-color);
    }

    .question-card.unanswered {
      border-left-color: var(--warning-color);
    }

    .question-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.25rem 1.5rem;
      background-color: #f8f9fa;
      border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .question-body {
      padding: 1.5rem;
    }

    .question-body h5 {
      font-weight: 600;
      color: var(--dark-text);
      margin-bottom: 0.75rem;
    }

    .question-body p {
      color: var(--dark-text);
      line-height: 1.6;
      margin-bottom: 0;
    }

    .reply-section {
      padding: 1.5rem;
      border-top: 1px solid #edf2f7;
      background-color: #f8fafc;
    }

    .reply-section h5 {
      font-weight: 600;
      color: var(--dark-text);
      margin-bottom: 0.75rem;
    }

    .badge {
      padding: 0.5rem 0.75rem;
      border-radius: 8px;
      font-weight: 500;
      font-size: 0.75rem;
    }

    .badge-subject {
      background-color: var(--primary-color);
    }

    .badge-grade {
      background-color: #20c997;
    }

    .btn {
      border-radius: 10px;
      padding: 0.5rem 1rem;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-success {
      background-color: var(--success-color);
      border-color: var(--success-color);
    }

    .btn-success:hover {
      background-color: #27ae60;
      border-color: #27ae60;
      transform: translateY(-2px);
    }

    .form-control {
      border-radius: 10px;
      padding: 0.75rem 1rem;
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
    }

    textarea.form-control {
      min-height: 100px;
    }

    /* Notification styles */
    .notification-container {
      position: relative;
    }

    #notificationBtn {
      background: white;
      border: none;
      width: 42px;
      height: 42px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }

    #notificationBtn:hover {
      background-color: #f8f9fa;
      transform: translateY(-2px);
    }

    #notificationBox {
      position: absolute;
      top: 100%;
      right: 0;
      width: 320px;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
      z-index: 1000;
      margin-top: 0.5rem;
      overflow: hidden;
      max-height: 400px;
      display: none;
    }

    #notificationBox .card-header {
      padding: 1rem 1.25rem;
      font-weight: 600;
    }

    .notification-item {
      padding: 1rem 1.25rem;
      border-bottom: 1px solid #edf2f7;
      transition: all 0.2s ease;
    }

    .notification-item:hover {
      background-color: #f8f9fa;
    }

    .notification-item .notification-title {
      font-weight: 500;
      margin-bottom: 0.25rem;
    }

    .notification-item .notification-time {
      font-size: 0.8rem;
      color: var(--light-text);
    }

    .empty-state {
      text-align: center;
      padding: 3rem 1.5rem;
    }

    .empty-state i {
      font-size: 3rem;
      color: #cbd5e0;
      margin-bottom: 1rem;
    }

    .empty-state p {
      font-weight: 500;
      margin-bottom: 0.5rem;
    }

    @media (max-width: 768px) {
      .content {
        margin-left: 0;
        padding: 15px;
      }

      .filter-bar {
        flex-direction: column;
        align-items: stretch;
      }

      .filter-controls {
        flex-direction: column;
        align-items: stretch;
      }

      #notificationBox {
        width: 280px;
        right: -10px;
      }
    }
  </style>
</head>
<body>

<div class="content">
  <!-- Notification Icon -->
  <div class="position-fixed top-0 end-0 p-3" style="z-index: 10000;">
    <div class="notification-container">
      <button class="btn position-relative" id="notificationBtn" onclick="toggleNotificationBox()">
        <i class="fas fa-bell"></i>
        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notificationCount">
          0
        </span>
      </button>

      <!-- Notification dropdown box -->
      <div id="notificationBox" class="card">
        <div class="card-header bg-primary text-white">
          <i class="fas fa-bell me-2"></i>Notifications
        </div>
        <ul class="list-group list-group-flush" id="notificationList"></ul>
      </div>
    </div>
  </div>

  <div class="page-header">
    <h2 class="mb-2"><i class="fas fa-comments me-2"></i>Student Questions</h2>
    <p class="mb-0">Answer student questions and provide guidance</p>
  </div>

  <div class="filter-bar">
    <div class="filter-controls">
      <select id="filterSubject" class="form-select" style="min-width: 150px;">
        <option value="">All Subjects</option>
      </select>
      <select id="filterGrade" class="form-select" style="min-width: 150px;">
        <option value="">All Grades</option>
      </select>
    </div>

    <div class="filter-status">
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="statusFilter" id="filterAll" value="all" checked>
        <label class="form-check-label" for="filterAll">All Questions</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="statusFilter" id="filterUnanswered" value="unanswered">
        <label class="form-check-label" for="filterUnanswered">Unanswered</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="statusFilter" id="filterAnswered" value="answered">
        <label class="form-check-label" for="filterAnswered">Answered</label>
      </div>
    </div>
  </div>

  <div id="questionList" class="mt-4"></div>
  <div id="noQuestionsMessage" class="alert alert-info d-none">
    <i class="fas fa-info-circle me-2"></i>No questions found matching your filters.
  </div>
</div>

<!-- Toast container for notifications -->
<div class="toast-container position-fixed top-0 end-0 p-3" id="toastContainer"></div>

<script>
// Global variables to store all questions and filters
let allQuestions = [];
let currentSubject = '';
let currentGrade = '';
let currentStatus = 'all';
let unreadCount = 0;

// Initialize filters when page loads
document.addEventListener('DOMContentLoaded', function() {
 
  document.getElementById('filterSubject').addEventListener('change', applyFilters);
  document.getElementById('filterGrade').addEventListener('change', applyFilters);

  const statusRadios = document.querySelectorAll('input[name="statusFilter"]');
  statusRadios.forEach(radio => {
    radio.addEventListener('change', applyFilters);
  });

  // Load questions
  loadQuestions();

  // Initialize notifications
  fetchNotifications();
  setInterval(fetchNotifications, 60000); // Check every minute
});

function toggleNotificationBox() {
  const notificationBox = document.getElementById('notificationBox');
  if (notificationBox.style.display === 'none' || notificationBox.style.display === '') {
    notificationBox.style.display = 'block';
    // Mark notifications as read when opened
    if (unreadCount > 0) {
      fetch('../../backend/notification/mark_as_read.php')
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            document.getElementById('notificationCount').style.display = 'none';
            unreadCount = 0;
          }
        });
    }
  } else {
    notificationBox.style.display = 'none';
  }
}

function fetchNotifications() {
  fetch('../../backend/notification/fetch_notifications.php')
    .then(res => res.json())
    .then(data => {
      console.log('Notifications data:', data); // Debug log

      unreadCount = data.length;
      const notificationCount = document.getElementById('notificationCount');
      const notificationList = document.getElementById('notificationList');

      notificationCount.textContent = unreadCount;
      notificationCount.style.display = unreadCount > 0 ? 'inline-block' : 'none';
      notificationList.innerHTML = '';

      if (data.length === 0) {
        notificationList.innerHTML = `
          <li class="list-group-item text-center py-4">
            <i class="fas fa-bell-slash text-muted mb-2" style="font-size: 2rem;"></i>
            <p class="mb-0">No new notifications</p>
          </li>
        `;
      } else {
        data.forEach(notification => {
          const li = document.createElement('li');
          li.className = 'list-group-item notification-item';

          
          const timeStamp = notification.scheduled_time || notification.created_at;
          const timeAgo = timeStamp ? getTimeAgo(new Date(timeStamp)) : 'Recently';

          li.innerHTML = `
            <div class="notification-title">
              <i class="fas fa-question-circle text-primary me-2"></i>
              ${notification.message}
            </div>
            <div class="notification-time">
              <i class="far fa-clock me-1"></i>${timeAgo}
            </div>
          `;

          notificationList.appendChild(li);
        });
      }
    })
    .catch(error => {
      console.error('Error fetching notifications:', error);
      
      const notificationList = document.getElementById('notificationList');
      notificationList.innerHTML = `
        <li class="list-group-item text-center py-4">
          <i class="fas fa-exclamation-circle text-danger mb-2" style="font-size: 2rem;"></i>
          <p class="mb-0">Error loading notifications</p>
          <small class="text-muted">Please try again later</small>
        </li>
      `;
    });
}

function getTimeAgo(date) {
  const seconds = Math.floor((new Date() - date) / 1000);

  let interval = Math.floor(seconds / 31536000);
  if (interval > 1) return interval + ' years ago';
  if (interval === 1) return '1 year ago';

  interval = Math.floor(seconds / 2592000);
  if (interval > 1) return interval + ' months ago';
  if (interval === 1) return '1 month ago';

  interval = Math.floor(seconds / 86400);
  if (interval > 1) return interval + ' days ago';
  if (interval === 1) return '1 day ago';

  interval = Math.floor(seconds / 3600);
  if (interval > 1) return interval + ' hours ago';
  if (interval === 1) return '1 hour ago';

  interval = Math.floor(seconds / 60);
  if (interval > 1) return interval + ' minutes ago';
  if (interval === 1) return '1 minute ago';

  return 'Just now';
}

async function loadQuestions() {
  try {
    // Show loading indicator
    document.getElementById('questionList').innerHTML = `
      <div class="d-flex justify-content-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    `;

    const res = await fetch('../../backend/forum/get_questions.php');
    const data = await res.json();

    
    console.log('Response from get_questions.php:', data);

    // Check if the response contains an error
    if (data.error) {
      document.getElementById('questionList').innerHTML = `
        <div class="empty-state">
          <i class="fas fa-exclamation-circle"></i>
          <p>${data.error}</p>
          <small class="text-muted">Please check your connection and try again</small>
        </div>
      `;
      return;
    }

    // Handle the case where data is directly an array (not wrapped in a questions property)
    const questions = Array.isArray(data.questions) ? data.questions :
                     (Array.isArray(data) ? data : []);

    if (questions.length === 0) {
      document.getElementById('questionList').innerHTML = `
        <div class="empty-state">
          <i class="fas fa-question-circle"></i>
          <p>No questions available</p>
          <small class="text-muted">Ask your students to start asking questions</small>
        </div>
      `;
      return;
    }

    // Process questions to ensure they have required fields
    const processedQuestions = questions
      .filter(q => q && (q.subject_name || q.subject_id))
      .map(q => ({
        ...q,
        subject_name: q.subject_name || q.subject_id,
        grade: q.grade || 'Unknown'
      }));

  
    allQuestions = processedQuestions;

    // Count unanswered questions for notification badge
    const unansweredCount = processedQuestions.filter(q => !q.reply_text).length;
    document.getElementById('notificationCount').textContent = unansweredCount;
    document.getElementById('notificationCount').style.display = unansweredCount > 0 ? 'inline-block' : 'none';

    
    populateFilters(processedQuestions);

    // Apply filters 
    applyFilters();
  } catch (error) {
    console.error('Error loading questions:', error);
    document.getElementById('questionList').innerHTML = `
      <div class="empty-state">
        <i class="fas fa-exclamation-circle"></i>
        <p>Error loading questions: ${error.message}</p>
        <small class="text-muted">Please try refreshing the page or check the console for details</small>
      </div>
    `;
  }
}

function populateFilters(questions) {
  try {
    const subjectFilter = document.getElementById('filterSubject');
    const gradeFilter = document.getElementById('filterGrade');

    // Clear existing options except the first one 
    while (subjectFilter.options.length > 1) {
      subjectFilter.remove(1);
    }

    while (gradeFilter.options.length > 1) {
      gradeFilter.remove(1);
    }

    if (!Array.isArray(questions) || questions.length === 0) {
      return;
    }

    
    const subjects = [...new Set(questions.map(q => q.subject_name || q.subject_id).filter(Boolean))];
    const grades = [...new Set(questions.map(q => q.grade).filter(Boolean))];

    // subject filter
    subjects.forEach(subject => {
      if (subject) {
        const option = document.createElement('option');
        option.value = subject;
        option.textContent = subject;
        subjectFilter.appendChild(option);
      }
    });

    // grade filter
    grades.forEach(grade => {
      if (grade) {
        const option = document.createElement('option');
        option.value = grade;
        option.textContent = `Grade ${grade}`;
        gradeFilter.appendChild(option);
      }
    });
  } catch (error) {
    console.error('Error populating filters:', error);
  }
}

function applyFilters() {
  try {
    
    currentSubject = document.getElementById('filterSubject').value;
    currentGrade = document.getElementById('filterGrade').value;

    // Get selected radio button with fallback
    const selectedRadio = document.querySelector('input[name="statusFilter"]:checked');
    currentStatus = selectedRadio ? selectedRadio.value : 'all';

  
    if (!Array.isArray(allQuestions)) {
      console.error('allQuestions is not an array:', allQuestions);
      displayQuestions([]);
      return;
    }

    // Filter questions with error handling
    const filteredQuestions = allQuestions.filter(q => {
      try {
        const subjectMatch = !currentSubject ||
                            (q.subject_name === currentSubject || q.subject_id === currentSubject);
        const gradeMatch = !currentGrade || q.grade === currentGrade;
        const statusMatch = currentStatus === 'all' ||
                          (currentStatus === 'answered' && q.reply_text) ||
                          (currentStatus === 'unanswered' && !q.reply_text);

        return subjectMatch && gradeMatch && statusMatch;
      } catch (err) {
        console.error('Error filtering question:', err, q);
        return false;
      }
    });

    // Display filtered questions
    displayQuestions(filteredQuestions);

    // Update filter counts
    updateFilterCounts(filteredQuestions.length, allQuestions.length);
  } catch (error) {
    console.error('Error applying filters:', error);
    document.getElementById('questionList').innerHTML =
      `<div class="alert alert-danger">Error filtering questions: ${error.message}</div>`;
  }
}


function updateFilterCounts(filteredCount, totalCount) {
  const countDisplay = document.createElement('div');
  countDisplay.className = 'text-muted small mt-2 filter-count-display';
  countDisplay.innerHTML = `Showing ${filteredCount} of ${totalCount} questions`;

  // Remove any existing count display
  const existingCount = document.querySelector('.filter-count-display');
  if (existingCount) {
    existingCount.remove();
  }

  // Add the new count display to the filter bar
  const filterBar = document.querySelector('.filter-bar');
  if (filterBar) {
    filterBar.appendChild(countDisplay);
  }
}

function displayQuestions(questions) {
  const questionList = document.getElementById('questionList');
  const noQuestionsMessage = document.getElementById('noQuestionsMessage');

  //  check for questions array
  if (!questions || !Array.isArray(questions) || questions.length === 0) {
    questionList.innerHTML = '';
    noQuestionsMessage.classList.remove('d-none');
    return;
  }

  noQuestionsMessage.classList.add('d-none');

  let html = '';
  questions.forEach(q => {
    try {
      //  get date values with fallbacks
      const date = q.created_at ? new Date(q.created_at).toLocaleDateString() : 'Unknown date';
      const repliedDate = q.replied_at ? new Date(q.replied_at).toLocaleDateString() : '';
      const statusClass = q.reply_text ? 'answered' : 'unanswered';

      //  get subject and grade with fallbacks
      const subjectDisplay = q.subject_name || q.subject_id || 'Unknown Subject';
      const gradeDisplay = q.grade || 'Unknown';

      html += `
        <div class="question-card ${statusClass}">
          <div class="question-header">
            <div>
              <span class="badge badge-subject bg-primary me-2">${subjectDisplay}</span>
              <span class="badge badge-grade bg-secondary">Grade ${gradeDisplay}</span>
              ${q.first_name ? `<span class="ms-2 text-muted">Student: ${q.first_name} ${q.last_name}</span>` : ''}
            </div>
            <small class="text-muted">Asked on: ${date}</small>
          </div>

          <div class="question-body">
            <h5>Question:</h5>
            <p>${q.question_text || 'No question text'}</p>
          </div>

          <div class="reply-section">
            <h5>Answer:</h5>
            ${q.reply_text ?
              `<p>${q.reply_text}</p>
               <small class="text-muted">Answered on: ${repliedDate}</small>` :
              `<form id="replyForm-${q.id}" class="mt-3" onsubmit="submitReply(event, ${q.id})">
                 <div class="mb-3">
                   <textarea class="form-control" name="reply" rows="3" placeholder="Write your answer here..." required></textarea>
                 </div>
                 <button type="submit" class="btn btn-success">
                   <i class="fas fa-paper-plane me-1"></i> Submit Answer
                 </button>
               </form>`
            }
          </div>
        </div>
      `;
    } catch (err) {
      console.error('Error rendering question:', err, q);
      // Add a placeholder for malformed questions
      html += `
        <div class="question-card border-danger">
          <div class="question-body">
            <div class="alert alert-warning">
              <i class="fas fa-exclamation-triangle me-2"></i>
              There was an error displaying this question.
            </div>
          </div>
        </div>
      `;
    }
  });

  questionList.innerHTML = html || '<div class="alert alert-info">No questions available.</div>';
}

async function submitReply(e, id) {
  e.preventDefault();
  const form = e.target;
  const reply = form.reply.value;

  // Disable form while submitting
  const submitBtn = form.querySelector('button[type="submit"]');
  const originalBtnText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';
  submitBtn.disabled = true;

  try {
    const formData = new FormData();
    formData.append('id', id);
    formData.append('reply', reply);

    const res = await fetch('../../backend/forum/reply_question.php', {
      method: 'POST',
      body: formData
    });

    // Check if response is JSON
    const contentType = res.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      // Handle non-JSON response
      const text = await res.text();
      console.error('Server returned non-JSON response:', text);
      throw new Error('Server returned invalid response format. Check console for details.');
    }

    const data = await res.json();
    if (data.success) {
      // Show success message
      const successDiv = document.createElement('div');
      successDiv.className = 'alert alert-success mt-3';
      successDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i> Your answer has been submitted successfully!';
      form.parentNode.appendChild(successDiv);

      // Remove success message after 3 seconds and reload questions
      setTimeout(() => {
        successDiv.remove();
        loadQuestions();
      }, 3000);
    } else {
      // Show error message with specific message if available
      const errorMessage = data.message || 'Failed to submit answer. Please try again.';
      console.error('Server error:', errorMessage);

      const errorDiv = document.createElement('div');
      errorDiv.className = 'alert alert-danger mt-3';
      errorDiv.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i> ${errorMessage}`;
      form.parentNode.appendChild(errorDiv);

      // Remove error message after 3 seconds
      setTimeout(() => {
        errorDiv.remove();
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
      }, 3000);
    }
  } catch (error) {
    console.error('Error submitting reply:', error);

    // Create error message element
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger mt-3';
    errorDiv.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i> Error submitting reply. Please try again later.';
    form.parentNode.appendChild(errorDiv);

    // Remove error message after 3 seconds
    setTimeout(() => {
      errorDiv.remove();
      submitBtn.innerHTML = originalBtnText;
      submitBtn.disabled = false;
    }, 3000);
  }
}
</script>

</body>
</html>
