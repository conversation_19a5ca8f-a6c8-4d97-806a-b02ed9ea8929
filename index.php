<?php
// Main entry point for Gurukula LMS
// This file checks server status and redirects appropriately

// Function to check if MySQL server is running
function checkMySQLServer() {
    $connection = @new mysqli("localhost", "root", "");
    if ($connection->connect_error) {
        return false;
    }
    $connection->close();
    return true;
}

// Function to check if database exists
function checkDatabase() {
    $connection = @new mysqli("localhost", "root", "", "institution_db");
    if ($connection->connect_error) {
        return false;
    }
    $connection->close();
    return true;
}

// Check server status
$mysql_running = checkMySQLServer();
$database_available = checkDatabase();

// If MySQL is not running, show server error
if (!$mysql_running) {
    header("Location: server_error.php");
    exit();
}

// If MySQL is running but database is not available, show database error
if (!$database_available) {
    header("Location: database_error.php");
    exit();
}

// If everything is working, redirect to home page
header("Location: frontend/home.php");
exit();
?>
