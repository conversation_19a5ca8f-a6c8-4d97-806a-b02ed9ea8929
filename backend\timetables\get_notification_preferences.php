<?php
session_start();
include_once '../../backend/server/db_connect.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Check if user is logged in as student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get student information including notification preferences
    $stmt = $conn->prepare("
        SELECT s.student_id, s.email_notifications, s.popup_notifications, u.email
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE s.user_id = ?
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception("No student record found for this user");
    }

    $student = $result->fetch_assoc();

    // Format preferences for the frontend
    $preferences = [
        'student_id' => $student['student_id'],
        'email_notifications' => $student['email_notifications'],
        'browser_notifications' => $student['popup_notifications'],
        'reminder_timing' => '1', // Default to 1 day before
        'notification_email' => $student['email']
    ];

    echo json_encode([
        'success' => true,
        'preferences' => $preferences
    ]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

$conn->close();
?>
