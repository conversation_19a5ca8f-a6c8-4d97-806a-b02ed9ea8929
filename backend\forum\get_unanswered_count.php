<?php
session_start();
include_once '../../backend/server/db_connect.php';

$user_id = $_SESSION['user_id'] ?? 0;
$role = $_SESSION['role'] ?? '';

if ($role !== 'teacher') {
    echo json_encode(['count' => 0]);
    exit;
}

// Get teacher ID
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$teacher_id = $teacher['teacher_id'] ?? 0;

// Get count of unanswered questions for this teacher's subjects
$query = "
    SELECT COUNT(*) as count 
    FROM forum_questions q
    JOIN teacher_subjects ts ON q.subject_id = ts.subject_id
    WHERE ts.teacher_id = ? AND q.reply_text IS NULL
";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $teacher_id);
$stmt->execute();
$result = $stmt->get_result();
$data = $result->fetch_assoc();

echo json_encode(['count' => (int)$data['count']]);
$stmt->close();
$conn->close();
?>