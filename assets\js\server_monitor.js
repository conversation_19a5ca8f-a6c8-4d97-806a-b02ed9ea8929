/**
 * Server Monitor - Checks server status and shows notifications
 * Include this script in pages that need server monitoring
 */

class ServerMonitor {
    constructor(options = {}) {
        this.checkInterval = options.checkInterval || 60000; // Check every minute
        this.showNotifications = options.showNotifications !== false; // Default true
        this.redirectOnError = options.redirectOnError !== false; // Default true
        this.isChecking = false;
        this.lastStatus = 'unknown';
        this.checkCount = 0;
        
        this.init();
    }

    init() {
        // Start monitoring
        this.startMonitoring();
        
        // Add visibility change listener to check when tab becomes active
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkServerStatus();
            }
        });
    }

    startMonitoring() {
        // Initial check
        this.checkServerStatus();
        
        // Set up interval
        setInterval(() => {
            this.checkServerStatus();
        }, this.checkInterval);
    }

    async checkServerStatus() {
        if (this.isChecking) return;
        
        this.isChecking = true;
        this.checkCount++;
        
        try {
            // Check if we can reach a simple endpoint
            const response = await fetch('/gurukula_lms/backend/server/health_check.php', {
                method: 'GET',
                cache: 'no-cache',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.handleServerResponse(data);
            } else {
                this.handleServerError('HTTP Error: ' + response.status);
            }
        } catch (error) {
            this.handleServerError('Network Error: ' + error.message);
        } finally {
            this.isChecking = false;
        }
    }

    handleServerResponse(data) {
        if (data.status === 'ok') {
            if (this.lastStatus === 'error') {
                this.showNotification('Server is back online!', 'success');
            }
            this.lastStatus = 'ok';
        } else {
            this.handleServerError(data.message || 'Unknown server error');
        }
    }

    handleServerError(errorMessage) {
        console.warn('Server Monitor: ' + errorMessage);
        
        if (this.lastStatus !== 'error') {
            if (this.showNotifications) {
                this.showNotification('Connection to server lost. Checking...', 'warning');
            }
        }
        
        this.lastStatus = 'error';
        
        // After multiple failed checks, redirect to error page
        if (this.checkCount >= 3 && this.redirectOnError) {
            this.redirectToErrorPage();
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `server-notification server-notification-${type}`;
        notification.innerHTML = `
            <div class="server-notification-content">
                <i class="fas fa-${this.getIconForType(type)} me-2"></i>
                <span>${message}</span>
                <button class="server-notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add styles if not already added
        this.addNotificationStyles();

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    getIconForType(type) {
        const icons = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'error': 'times-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    addNotificationStyles() {
        if (document.getElementById('server-monitor-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'server-monitor-styles';
        styles.textContent = `
            .server-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                max-width: 400px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                animation: slideInRight 0.3s ease-out;
            }

            .server-notification-content {
                padding: 12px 16px;
                display: flex;
                align-items: center;
                color: white;
                font-weight: 500;
            }

            .server-notification-success .server-notification-content {
                background: linear-gradient(135deg, #28a745, #20c997);
            }

            .server-notification-warning .server-notification-content {
                background: linear-gradient(135deg, #ffc107, #fd7e14);
            }

            .server-notification-error .server-notification-content {
                background: linear-gradient(135deg, #dc3545, #e83e8c);
            }

            .server-notification-info .server-notification-content {
                background: linear-gradient(135deg, #17a2b8, #6f42c1);
            }

            .server-notification-close {
                background: none;
                border: none;
                color: white;
                margin-left: auto;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                opacity: 0.8;
                transition: opacity 0.2s;
            }

            .server-notification-close:hover {
                opacity: 1;
                background: rgba(255, 255, 255, 0.1);
            }

            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @media (max-width: 768px) {
                .server-notification {
                    left: 20px;
                    right: 20px;
                    min-width: auto;
                    max-width: none;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    redirectToErrorPage() {
        if (this.showNotifications) {
            this.showNotification('Redirecting to error page...', 'error');
        }
        
        setTimeout(() => {
            window.location.href = '/gurukula_lms/server_error.php';
        }, 2000);
    }

    // Public method to manually check status
    checkNow() {
        this.checkServerStatus();
    }

    // Public method to stop monitoring
    stop() {
        clearInterval(this.intervalId);
    }
}

// Auto-initialize if not in error pages
if (!window.location.pathname.includes('error.php')) {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.serverMonitor = new ServerMonitor();
        });
    } else {
        window.serverMonitor = new ServerMonitor();
    }
}
