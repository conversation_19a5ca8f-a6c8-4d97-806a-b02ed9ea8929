<?php
session_start();
include_once '../server/db_connect.php';
header('Content-Type: application/json');


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'worker') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}


$student_id = isset($_POST['student_id']) ? intval($_POST['student_id']) : 0;
$subject_id = isset($_POST['subject_id']) ? intval($_POST['subject_id']) : 0;
$payment_period = isset($_POST['payment_period']) ? $_POST['payment_period'] : '';
$next_payment_due = isset($_POST['next_payment_due']) ? $_POST['next_payment_due'] : '';
$action = isset($_POST['action']) ? $_POST['action'] : '';

if (!$student_id || !$subject_id || !$payment_period || !$action) {
    echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
    exit();
}

try {
    // Start transaction
    $conn->begin_transaction();

    if ($action === 'mark_paid') {
        if (empty($next_payment_due)) {
            throw new Exception('Next payment due date is required');
        }

        // Create a payment record
        $payment_insert = $conn->prepare("
            INSERT INTO payment_records
            (student_id, subject_id, payment_period, payment_date, payment_method, amount, status)
            VALUES (?, ?, ?, NOW(), 'Manual',
            (SELECT fee FROM subjects WHERE subject_id = ?), 'PAID')
        ");
        $payment_insert->bind_param("iisi", $student_id, $subject_id, $payment_period, $subject_id);
        if (!$payment_insert->execute()) {
            throw new Exception('Failed to create payment record: ' . $payment_insert->error);
        }

        
        $update_subject = $conn->prepare("
            UPDATE student_subjects
            SET payment_status = 'PAID',
                last_payment_date = NOW(),
                last_payment_period = ?
            WHERE student_id = ? AND subject_id = ?
        ");
        $update_subject->bind_param("sii", $payment_period, $student_id, $subject_id);
        if (!$update_subject->execute()) {
            throw new Exception('Failed to update subject payment status: ' . $update_subject->error);
        }

       
        $check_all_paid = $conn->prepare("
            SELECT COUNT(*) as total_subjects,
                   SUM(CASE WHEN payment_status = 'PAID' THEN 1 ELSE 0 END) as paid_subjects
            FROM student_subjects
            WHERE student_id = ?
        ");
        $check_all_paid->bind_param("i", $student_id);
        $check_all_paid->execute();
        $result = $check_all_paid->get_result();
        $payment_counts = $result->fetch_assoc();

       
        if ($payment_counts['total_subjects'] == $payment_counts['paid_subjects']) {
            $update_student = $conn->prepare("
                UPDATE students
                SET payment_status = 'PAID',
                    last_payment_date = NOW(),
                    last_payment_period = ?,
                    next_payment_due = ?
                WHERE student_id = ?
            ");
            $update_student->bind_param("ssi", $payment_period, $next_payment_due, $student_id);
            if (!$update_student->execute()) {
                throw new Exception('Failed to update student payment status: ' . $update_student->error);
            }
        }

        // Payment has been logged in payment_records table
        $worker_id = $_SESSION['user_id'];

        $conn->commit();
        echo json_encode(['success' => true]);
    }
    else if ($action === 'mark_unpaid') {
       
        $payment_update = $conn->prepare("
            UPDATE payment_records
            SET status = 'CANCELLED', cancelled_date = NOW()
            WHERE student_id = ? AND subject_id = ? AND payment_period = ? AND status = 'PAID'
        ");
        $payment_update->bind_param("iis", $student_id, $subject_id, $payment_period);
        if (!$payment_update->execute()) {
            throw new Exception('Failed to update payment record: ' . $payment_update->error);
        }

       
        $update_subject = $conn->prepare("
            UPDATE student_subjects
            SET payment_status = 'UNPAID',
                last_payment_date = NOW()
            WHERE student_id = ? AND subject_id = ?
        ");
        $update_subject->bind_param("ii", $student_id, $subject_id);
        if (!$update_subject->execute()) {
            throw new Exception('Failed to update subject payment status: ' . $update_subject->error);
        }

       
        $check_any_paid = $conn->prepare("
            SELECT COUNT(*) as paid_subjects
            FROM student_subjects
            WHERE student_id = ? AND payment_status = 'PAID'
        ");
        $check_any_paid->bind_param("i", $student_id);
        $check_any_paid->execute();
        $result = $check_any_paid->get_result();
        $payment_count = $result->fetch_assoc();

        //  update the global payment status to UNPAID if no subjects are paid
        if ($payment_count['paid_subjects'] == 0) {
            $update_student = $conn->prepare("
                UPDATE students
                SET payment_status = 'UNPAID'
                WHERE student_id = ?
            ");
            $update_student->bind_param("i", $student_id);
            if (!$update_student->execute()) {
                throw new Exception('Failed to update student payment status: ' . $update_student->error);
            }
        }

        
        $worker_id = $_SESSION['user_id'];

        $conn->commit();
        echo json_encode(['success' => true]);
    }
    else {
        throw new Exception('Invalid action');
    }
}
catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

$conn->close();
?>

