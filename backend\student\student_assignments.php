<?php
session_start();
include '../../backend/server/db_connect.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../backend/server/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get student_id and grade
$stmt = $conn->prepare("SELECT student_id, grade FROM students WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();
$student_id = $student['student_id'];
$grade = "Grade " . $student['grade']; // FIXED: Add 'Grade ' prefix
$stmt->close();

// Handle submission
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_FILES['submission_file'])) {
    $assignment_id = $_POST['assignment_id'];
    $uploadDir = '../../uploads/submissions/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    $filename = time() . '_' . basename($_FILES['submission_file']['name']);
    $uploadPath = $uploadDir . $filename;
    $relativePath = 'uploads/submissions/' . $filename;

    if (move_uploaded_file($_FILES['submission_file']['tmp_name'], $uploadPath)) {
      
        $check = $conn->prepare("SELECT * FROM submissions WHERE assignment_id = ? AND student_id = ?");
        $check->bind_param("ii", $assignment_id, $student_id);
        $check->execute();
        $check_result = $check->get_result();

        if ($check_result->num_rows === 0) {
            $stmt = $conn->prepare("INSERT INTO submissions (assignment_id, student_id, file_path) VALUES (?, ?, ?)");
            $stmt->bind_param("iis", $assignment_id, $student_id, $relativePath);
            $stmt->execute();
            $stmt->close();
        }
    }
}


$subjects = [];
$stmt = $conn->prepare("
    SELECT s.subject_id, s.subject_name
    FROM subjects s
    JOIN student_subjects ss ON s.subject_id = ss.subject_id
    WHERE ss.student_id = ?
");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$subjects_result = $stmt->get_result();
while ($row = $subjects_result->fetch_assoc()) {
    $subjects[] = $row;
}
$stmt->close();


$stmt = $conn->prepare("
    SELECT a.*, s.subject_name
    FROM assignments a
    JOIN subjects s ON a.subject_id = s.subject_id
    JOIN student_subjects ss ON s.subject_id = ss.subject_id
    WHERE a.grade = ? AND ss.student_id = ?
    ORDER BY a.posted_at DESC
");
$stmt->bind_param("si", $grade, $student_id);
$stmt->execute();
$assignments = $stmt->get_result();
?>
