

document.addEventListener('DOMContentLoaded', function() {
  // Create mobile menu toggle button if it doesn't exist
  if (!document.querySelector('.mobile-menu-toggle')) {
    const toggleButton = document.createElement('button');
    toggleButton.className = 'mobile-menu-toggle';
    toggleButton.innerHTML = '<i class="fas fa-bars"></i>';
    document.body.appendChild(toggleButton);
    
    // Add event listener to toggle button
    toggleButton.addEventListener('click', toggleSidebar);
  }
  
  // Add click event to existing toggle button if it exists
  const existingToggle = document.querySelector('.mobile-menu-toggle');
  if (existingToggle) {
    existingToggle.addEventListener('click', toggleSidebar);
  }
  
  // Add overlay for mobile menu if it doesn't exist
  if (!document.querySelector('.sidebar-overlay')) {
    const overlay = document.createElement('div');
    overlay.className = 'sidebar-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '1030';
    overlay.style.display = 'none';
    document.body.appendChild(overlay);
    
    // Add event listener to overlay
    overlay.addEventListener('click', toggleSidebar);
  }
  
  // Check window size on load and resize
  handleWindowResize();
  window.addEventListener('resize', handleWindowResize);
  
  // Close sidebar when clicking on a link (mobile only)
  const sidebarLinks = document.querySelectorAll('.sidebar a');
  sidebarLinks.forEach(link => {
    link.addEventListener('click', function() {
      if (window.innerWidth <= 768) {
        toggleSidebar();
      }
    });
  });
});


function toggleSidebar() {
  const sidebar = document.querySelector('.sidebar');
  const overlay = document.querySelector('.sidebar-overlay');
  
  if (sidebar) {
    sidebar.classList.toggle('active');
    
    if (overlay) {
      if (sidebar.classList.contains('active')) {
        overlay.style.display = 'block';
      } else {
        overlay.style.display = 'none';
      }
    }
  }
}


function handleWindowResize() {
  const sidebar = document.querySelector('.sidebar');
  const overlay = document.querySelector('.sidebar-overlay');
  
  if (sidebar) {
    if (window.innerWidth <= 768) {
      // Mobile view
      sidebar.classList.remove('collapsed');
      if (!sidebar.classList.contains('active')) {
        if (overlay) overlay.style.display = 'none';
      }
    } else if (window.innerWidth <= 992) {
      // Tablet view - collapsed sidebar
      sidebar.classList.add('collapsed');
      sidebar.classList.remove('active');
      if (overlay) overlay.style.display = 'none';
    } else {
      // Desktop view - full sidebar
      sidebar.classList.remove('collapsed');
      sidebar.classList.remove('active');
      if (overlay) overlay.style.display = 'none';
    }
  }
}
