<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Down - Guru<PERSON>la LMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #7209b7;
            --error-color: #dc3545;
            --warning-color: #ffc107;
            --success-color: #198754;
            --dark-color: #212529;
            --light-color: #f8f9fa;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        .background-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape-1 {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape-2 {
            width: 120px;
            height: 120px;
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape-3 {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape-4 {
            width: 100px;
            height: 100px;
            bottom: 10%;
            right: 20%;
            animation-delay: 1s;
        }

        .shape-5 {
            width: 140px;
            height: 140px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .error-container {
            position: relative;
            z-index: 2;
            max-width: 600px;
            width: 90%;
            margin: 0 auto;
        }

        .error-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--error-color), #ff6b6b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: pulse 2s infinite;
        }

        .error-icon i {
            font-size: 3rem;
            color: white;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
            }
        }

        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1rem;
        }

        .error-message {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .status-check {
            background: var(--light-color);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid var(--warning-color);
        }

        .status-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 500;
            color: var(--dark-color);
        }

        .status-indicator {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-down {
            background: rgba(220, 53, 69, 0.1);
            color: var(--error-color);
        }

        .status-checking {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
        }

        .btn-retry {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin-right: 1rem;
        }

        .btn-retry:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(67, 97, 238, 0.3);
            color: white;
        }

        .btn-home {
            background: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-home:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .auto-refresh {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(25, 135, 84, 0.1);
            border-radius: 10px;
            color: var(--success-color);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .error-card {
                padding: 2rem 1.5rem;
            }

            .error-title {
                font-size: 2rem;
            }

            .error-icon {
                width: 100px;
                height: 100px;
            }

            .error-icon i {
                font-size: 2.5rem;
            }

            .btn-retry, .btn-home {
                display: block;
                width: 100%;
                margin: 0.5rem 0;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="background-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
    </div>

    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-server"></i>
            </div>
            
            <h1 class="error-title">Server is Down</h1>
            <p class="error-message">
                We're sorry, but the Gurukula LMS server is currently unavailable. 
                This usually means XAMPP services are not running.
            </p>

            <div class="status-check">
                <div class="status-item">
                    <span class="status-label">
                        <i class="fas fa-database me-2"></i>MySQL Server
                    </span>
                    <span class="status-indicator status-down">
                        <i class="fas fa-times me-1"></i>Down
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">
                        <i class="fas fa-globe me-2"></i>Web Server
                    </span>
                    <span class="status-indicator status-checking">
                        <i class="fas fa-question me-1"></i>Checking...
                    </span>
                </div>
            </div>

            <div class="d-flex flex-wrap justify-content-center gap-2">
                <a href="javascript:location.reload()" class="btn-retry">
                    <i class="fas fa-redo-alt"></i>
                    Try Again
                </a>
                <a href="/gurukula_lms/frontend/home.php" class="btn-home">
                    <i class="fas fa-home"></i>
                    Go Home
                </a>
            </div>

            <div class="auto-refresh">
                <i class="fas fa-sync-alt me-2"></i>
                This page will automatically refresh every 30 seconds
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);

        // Check web server status
        fetch(window.location.origin + '/gurukula_lms/frontend/home.php')
            .then(response => {
                if (response.ok) {
                    document.querySelector('.status-item:last-child .status-indicator').innerHTML = 
                        '<i class="fas fa-check me-1"></i>Running';
                    document.querySelector('.status-item:last-child .status-indicator').className = 
                        'status-indicator bg-success text-white';
                }
            })
            .catch(error => {
                document.querySelector('.status-item:last-child .status-indicator').innerHTML = 
                    '<i class="fas fa-times me-1"></i>Down';
                document.querySelector('.status-item:last-child .status-indicator').className = 
                    'status-indicator status-down';
            });
    </script>
</body>
</html>
