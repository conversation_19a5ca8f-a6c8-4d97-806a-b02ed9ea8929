<?php

include_once '../../backend/server/db_connect.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Check if student_id is provided
if (!isset($_GET['student_id']) || empty($_GET['student_id'])) {
    echo json_encode(['error' => 'Student ID is required']);
    exit;
}

$student_id = intval($_GET['student_id']);

try {
    // Get subjects the student is enrolled in
    $subject_query = "SELECT s.subject_id, s.subject_name
                     FROM subjects s
                     JOIN student_subjects ss ON s.subject_id = ss.subject_id
                     WHERE ss.student_id = ?";
    $stmt = $conn->prepare($subject_query);
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $subjects_result = $stmt->get_result();

    $subjects = [];
    while ($row = $subjects_result->fetch_assoc()) {
        $subjects[] = [
            'id' => $row['subject_id'],
            'name' => $row['subject_name']
        ];
    }

    // Prepare data structures
    $attendance_data = [
        'subjects' => [],
        'percentages' => []
    ];

    $marks_data = [
        'subjects' => [],
        'values' => []
    ];

    // Get current month and year
    $current_month = date('m');
    $current_year = date('Y');

    // Calculate attendance percentages for each subject
    $total_attendance_percentage = 0;
    foreach ($subjects as $subject) {
        // Get total classes for this subject
        $total_classes_query = "SELECT COUNT(DISTINCT date) as total_classes
                               FROM attendance
                               WHERE subject_name = ?
                               AND MONTH(date) = ? AND YEAR(date) = ?";
        $stmt = $conn->prepare($total_classes_query);
        $stmt->bind_param("sii", $subject['name'], $current_month, $current_year);
        $stmt->execute();
        $total_result = $stmt->get_result();
        $total_row = $total_result->fetch_assoc();
        $total_classes = $total_row['total_classes'] > 0 ? $total_row['total_classes'] : 1; // Avoid division by zero

        // Get attended classes for this subject
        $attended_query = "SELECT COUNT(*) as attended_classes
                          FROM attendance
                          WHERE student_id = ? AND subject_name = ?
                          AND MONTH(date) = ? AND YEAR(date) = ?";
        $stmt = $conn->prepare($attended_query);
        $stmt->bind_param("isii", $student_id, $subject['name'], $current_month, $current_year);
        $stmt->execute();
        $attended_result = $stmt->get_result();
        $attended_row = $attended_result->fetch_assoc();
        $attended_classes = $attended_row['attended_classes'];

        // Calculate percentage
        $percentage = round(($attended_classes / $total_classes) * 100);

        $attendance_data['subjects'][] = $subject['name'];
        $attendance_data['percentages'][] = $percentage;

        $total_attendance_percentage += $percentage;
    }

    // Calculate average attendance
    $avg_attendance = count($subjects) > 0 ? round($total_attendance_percentage / count($subjects)) : 0;

    // Get marks for each subject
    $total_marks = 0;
    $marks_count = 0;
    $debug_info = [];

    foreach ($subjects as $subject) {
        // Get average marks for this subject - use CAST to ensure numeric conversion
        
        $marks_query = "SELECT AVG(CAST(s.marks AS DECIMAL(10,2))) as avg_marks
                       FROM submissions s
                       JOIN assignments a ON s.assignment_id = a.assignment_id
                       WHERE s.student_id = ?
                       AND a.subject_id = ?
                       AND s.marks IS NOT NULL
                       AND s.marks != ''";

        $stmt = $conn->prepare($marks_query);
        $stmt->bind_param("ii", $student_id, $subject['id']);
        $stmt->execute();
        $marks_result = $stmt->get_result();
        $marks_row = $marks_result->fetch_assoc();

        // Debug info
        $debug_info[] = [
            'subject_id' => $subject['id'],
            'subject_name' => $subject['name'],
            'avg_marks' => $marks_row['avg_marks'],
            'query' => $marks_query,
            'student_id' => $student_id
        ];

        $avg_subject_marks = $marks_row['avg_marks'] ? round($marks_row['avg_marks']) : 0;

        $marks_data['subjects'][] = $subject['name'];
        $marks_data['values'][] = $avg_subject_marks;

        if ($marks_row['avg_marks']) {
            $total_marks += $avg_subject_marks;
            $marks_count++;
        }
    }

    // Calculate average marks
    $avg_marks = $marks_count > 0 ? round($total_marks / $marks_count) : 0;

    // Get progress data for the last 6 months
    $progress_data = [
        'months' => [],
        'marks' => [],
        'attendance' => []
    ];

    // Use the first subject for monthly progress (this is a simplification)
    $first_subject = $subjects[0] ?? ['id' => 0, 'name' => ''];

    for ($i = 5; $i >= 0; $i--) {
        $month = date('m', strtotime("-$i months"));
        $year = date('Y', strtotime("-$i months"));
        $month_name = date('M Y', strtotime("-$i months"));

        // Get average marks for this month
        $monthly_marks_query = "SELECT AVG(CAST(s.marks AS DECIMAL(10,2))) as avg_marks
                              FROM submissions s
                              JOIN assignments a ON s.assignment_id = a.assignment_id
                              WHERE s.student_id = ?
                              AND a.subject_id = ?
                              AND MONTH(a.deadline) = ?
                              AND YEAR(a.deadline) = ?
                              AND s.marks IS NOT NULL
                              AND s.marks != ''";
        $stmt = $conn->prepare($monthly_marks_query);
        $stmt->bind_param("iiii", $student_id, $first_subject['id'], $month, $year);
        $stmt->execute();
        $monthly_marks_result = $stmt->get_result();
        $monthly_marks_row = $monthly_marks_result->fetch_assoc();

        $avg_monthly_marks = $monthly_marks_row['avg_marks'] ? round($monthly_marks_row['avg_marks']) : 0;

        $progress_data['months'][] = $month_name;
        $progress_data['marks'][] = $avg_monthly_marks;

        // Get attendance percentage for this month
        $attendance_query = "SELECT COUNT(DISTINCT date) as total_classes,
                                  COUNT(*) as attended_classes
                            FROM attendance
                            WHERE student_id = ? AND subject_name = ?
                            AND MONTH(date) = ? AND YEAR(date) = ?";
        $stmt = $conn->prepare($attendance_query);
        $stmt->bind_param("isii", $student_id, $first_subject['name'], $month, $year);
        $stmt->execute();
        $attendance_result = $stmt->get_result();
        $attendance_row = $attendance_result->fetch_assoc();

        $total_classes = $attendance_row['total_classes'] > 0 ? $attendance_row['total_classes'] : 1;
        $attended_classes = $attendance_row['attended_classes'];

        $attendance_percentage = round(($attended_classes / $total_classes) * 100);

        $progress_data['attendance'][] = $attendance_percentage;
    }

    // Prepare the final response
    $response = [
        'student_id' => $student_id,
        'subjects' => $subjects,
        'attendance' => $attendance_data,
        'marks' => $marks_data,
        'summary' => [
            'avgAttendance' => $avg_attendance,
            'avgMarks' => $avg_marks,
            'totalSubjects' => count($subjects)
        ],
        'progress' => $progress_data,
        'debug' => [
            'marks_debug' => $debug_info,
            'total_marks' => $total_marks,
            'marks_count' => $marks_count
        ]
    ];

    echo json_encode($response);
} catch (Exception $e) {
    echo json_encode(['error' => 'An error occurred: ' . $e->getMessage()]);
}


