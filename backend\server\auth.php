<?php

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// role-based access levels
define('ACCESS_PUBLIC', 0);     
define('ACCESS_STUDENT', 1);    
define('ACCESS_TEACHER', 2);    
define('ACCESS_PARENT', 3);     
define('ACCESS_WORKER', 4);     
define('ACCESS_OWNER', 5);    


$ROLE_ACCESS_LEVELS = [
    'student' => ACCESS_STUDENT,
    'teacher' => ACCESS_TEACHER,
    'parent' => ACCESS_PARENT,
    'worker' => ACCESS_WORKER,
    'owner' => ACCESS_OWNER
];

// page access requirements
$PAGE_ACCESS_REQUIREMENTS = [
    // Public pages
    'index.php' => ACCESS_PUBLIC,
    'frontend/login.php' => ACCESS_PUBLIC,
    'frontend/home.php' => ACCESS_PUBLIC,
    'backend/server/login.php' => ACCESS_PUBLIC,
    'backend/server/forgot_password.php' => ACCESS_PUBLIC,
    'backend/server/reset_password.php' => ACCESS_PUBLIC,

    // Student pages
    'frontend/dashboards/student_dashboard.php' => ACCESS_STUDENT,
    'frontend/student/student_marks.php' => ACCESS_STUDENT,
    'frontend/student/add_subject.php' => ACCESS_STUDENT,
    'frontend/student/add_parent.php' => ACCESS_STUDENT,
    'frontend/student/student_forum.php' => ACCESS_STUDENT,
    'frontend/student/assignments.php' => ACCESS_STUDENT,
    'frontend/settings/student_settings.php' => ACCESS_STUDENT,
    'frontend/timetables/student_timetable.php' => ACCESS_STUDENT,

    // Teacher pages
    'frontend/dashboards/teacher_dashboard.php' => ACCESS_TEACHER,
    'frontend/teacher/assignments.php' => ACCESS_TEACHER,
    'frontend/teacher/teacher_forum.php' => ACCESS_TEACHER,
    'frontend/teacher/manage_fees.php' => ACCESS_TEACHER,
    'frontend/teacher/subject_details.php' => ACCESS_TEACHER,
    'frontend/teacher/teacher_view_submissions.php' => ACCESS_TEACHER,
    'frontend/settings/teacher_settings.php' => ACCESS_TEACHER,
    'frontend/timetables/teacher_timetable.php' => ACCESS_TEACHER,

    // Parent pages
    'frontend/dashboards/parent_dashboard.php' => ACCESS_PARENT,
    'frontend/parent/student_performance.php' => ACCESS_PARENT,
    'frontend/payments/parent_payment.php' => ACCESS_PARENT,
    'frontend/payments/payment_success.php' => ACCESS_PARENT,
    'backend/payments/process_payment.php' => ACCESS_PARENT,
    'backend/payments/validate_student.php' => ACCESS_PARENT,
    'backend/payments/get_subject_fee.php' => ACCESS_PARENT,
    'frontend/settings/parent_settings.php' => ACCESS_PARENT,

    // Worker pages
    'frontend/dashboards/worker_dashboard.php' => ACCESS_WORKER,
    'frontend/worker/qr_scanner.php' => ACCESS_WORKER,
    'frontend/worker/monthly_attendance.php' => ACCESS_WORKER,
    'frontend/worker/monthly_payments.php' => ACCESS_WORKER,
    'frontend/settings/worker_settings.php' => ACCESS_WORKER,

    // Owner pages
    'frontend/dashboards/owner_dashboard.php' => ACCESS_OWNER,
    'frontend/timetables/owner_timetable.php' => ACCESS_OWNER,
    'frontend/financial_report.php' => ACCESS_OWNER,
    'frontend/settings/owner_settings.php' => ACCESS_OWNER
];


function hasPageAccess($page_path) {
    global $PAGE_ACCESS_REQUIREMENTS, $ROLE_ACCESS_LEVELS;

    
    $base_path = '/gurukula_lms/';
    if (strpos($page_path, $base_path) === 0) {
        $page_path = substr($page_path, strlen($base_path));
    }

    // default to requiring owner access 
    $required_access = $PAGE_ACCESS_REQUIREMENTS[$page_path] ?? ACCESS_OWNER;

    
    if ($required_access === ACCESS_PUBLIC) {
        return true;
    }

    
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) { //user login or not
        return false;
    }

    $user_role = $_SESSION['role'];
    $user_access_level = $ROLE_ACCESS_LEVELS[$user_role] ?? 0;

    
    if ($user_role === 'owner') {
        return true;
    }

    return $user_access_level === $required_access;
}


function enforcePageAccess($page_path = null) {
    if ($page_path === null) {
        $page_path = $_SERVER['REQUEST_URI'];
    }

    if (!hasPageAccess($page_path)) {
        // User doesn't have access, redirect to login page
        header("Location: " . getLoginRedirectPath());
        exit();
    }
}


function getLoginRedirectPath() {
    $current_path = $_SERVER['REQUEST_URI'];

    // how many directories up we need to go to reach the login page
    $path_depth = substr_count($current_path, '/') - 1;
    if (strpos($current_path, '/gurukula_lms/') !== false) {
        $path_depth = substr_count($current_path, '/') - substr_count('/gurukula_lms/', '/');
    }

    $path_prefix = '';
    for ($i = 0; $i < $path_depth; $i++) {
        $path_prefix .= '../';
    }

    return $path_prefix . 'frontend/login.php?unauthorized=1';
}


function addPageAccessRequirement($page_path, $access_level) {
    global $PAGE_ACCESS_REQUIREMENTS;
    $PAGE_ACCESS_REQUIREMENTS[$page_path] = $access_level;
}
?>
