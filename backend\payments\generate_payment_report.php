<?php
session_start();
include_once '../server/db_connect.php';
require_once '../../vendor/autoload.php'; 
require_once '../../libs/tcpdf/tcpdf.php'; //  TCPDF library


if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'worker') {
    header('Location: ../../frontend/login.php');
    exit();
}

$month = isset($_GET['month']) ? intval($_GET['month']) : date('n');
$year = isset($_GET['year']) ? intval($_GET['year']) : date('Y');
$subject_filter = isset($_GET['subject']) ? $_GET['subject'] : '';
$grade_filter = isset($_GET['grade']) ? $_GET['grade'] : '';

// Format the month and year for display
$month_name = date('F', mktime(0, 0, 0, $month, 1, $year));
$period = sprintf('%04d-%02d', $year, $month);

try {
    
    $query = "SELECT s.student_id, CONCAT(u.first_name, ' ', u.last_name) AS student_name, 
                     s.grade, s.payment_status, 
                     s.last_payment_date, s.last_payment_period, s.next_payment_due,
                     sub.subject_id, sub.subject_name
              FROM students s
              JOIN users u ON s.user_id = u.id
              JOIN student_subjects ss ON s.student_id = ss.student_id
              JOIN subjects sub ON ss.subject_id = sub.subject_id
              WHERE 1=1";

    $params = [];
    $types = '';

    if (!empty($subject_filter)) {
        $query .= " AND sub.subject_id = ?";
        $params[] = $subject_filter;
        $types .= 'i';
    }

    if (!empty($grade_filter)) {
        $query .= " AND s.grade = ?";
        $params[] = $grade_filter;
        $types .= 's';
    }

    $query .= " ORDER BY s.grade, u.first_name, u.last_name";

    $stmt = $conn->prepare($query);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();

    // Create PDF document
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

   
    $pdf->SetCreator('Gurukula LMS');
    $pdf->SetAuthor('Gurukula LMS');
    $pdf->SetTitle('Payment Report - ' . $month_name . ' ' . $year);
    $pdf->SetSubject('Monthly Payment Report');

   
    $pdf->SetHeaderData('', 0, 'GURUKULA Institution', 'Payment Report - ' . $month_name . ' ' . $year);

    
    $pdf->setHeaderFont(Array('helvetica', '', 10));
    $pdf->setFooterFont(Array('helvetica', '', 8));

   
    $pdf->SetDefaultMonospacedFont('courier');

    $pdf->SetMargins(15, 20, 15);
    $pdf->SetHeaderMargin(5);
    $pdf->SetFooterMargin(10);

  
    $pdf->SetAutoPageBreak(TRUE, 15);

    
    $pdf->AddPage();

    
    $pdf->SetFont('helvetica', 'B', 16);

    
    $pdf->Cell(0, 10, 'Monthly Payment Report', 0, 1, 'C');
    $pdf->SetFont('helvetica', '', 12);
    $pdf->Cell(0, 10, $month_name . ' ' . $year, 0, 1, 'C');

    
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(0, 10, 'Filters:', 0, 1);
    $pdf->SetFont('helvetica', '', 10);
    
    $subject_name = 'All Subjects';
    if (!empty($subject_filter)) {
        $subject_query = $conn->prepare("SELECT subject_name FROM subjects WHERE subject_id = ?");
        $subject_query->bind_param("i", $subject_filter);
        $subject_query->execute();
        $subject_result = $subject_query->get_result();
        if ($subject_result->num_rows > 0) {
            $subject_name = $subject_result->fetch_assoc()['subject_name'];
        }
    }
    
    $pdf->Cell(30, 7, 'Subject:', 0, 0);
    $pdf->Cell(0, 7, $subject_name, 0, 1);
    
    $pdf->Cell(30, 7, 'Grade:', 0, 0);
    $pdf->Cell(0, 7, !empty($grade_filter) ? $grade_filter : 'All Grades', 0, 1);
    
    // Add table header
    $pdf->Ln(10);
    $pdf->SetFillColor(230, 230, 230);
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(10, 7, '#', 1, 0, 'C', true);
    $pdf->Cell(60, 7, 'Student Name', 1, 0, 'C', true);
    $pdf->Cell(20, 7, 'Grade', 1, 0, 'C', true);
    $pdf->Cell(40, 7, 'Subject', 1, 0, 'C', true);
    $pdf->Cell(30, 7, 'Status', 1, 0, 'C', true);
    $pdf->Cell(30, 7, 'Last Payment', 1, 1, 'C', true);
    
    
    $pdf->SetFont('helvetica', '', 10);
    $i = 1;
    
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $pdf->Cell(10, 7, $i++, 1, 0, 'C');
            $pdf->Cell(60, 7, $row['student_name'], 1, 0, 'L');
            $pdf->Cell(20, 7, $row['grade'], 1, 0, 'C');
            $pdf->Cell(40, 7, $row['subject_name'], 1, 0, 'L');
            
            
            if ($row['payment_status'] == 'PAID' && $row['last_payment_period'] == $period) {
                $pdf->SetTextColor(0, 128, 0); // Green for paid
            } else {
                $pdf->SetTextColor(255, 0, 0); // Red for not paid
            }
            
            $pdf->Cell(30, 7, $row['payment_status'], 1, 0, 'C');
            $pdf->SetTextColor(0, 0, 0); 
            
            $last_payment = !empty($row['last_payment_date']) ? date('Y-m-d', strtotime($row['last_payment_date'])) : 'Never';
            $pdf->Cell(30, 7, $last_payment, 1, 1, 'C');
        }
    } else {
        $pdf->Cell(190, 7, 'No students found matching the criteria', 1, 1, 'C');
    }
    
    // Add summary section
    $pdf->Ln(10);
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell(0, 10, 'Payment Summary', 0, 1);
    
   
    $stats_query = "SELECT 
                      COUNT(*) as total_students,
                      SUM(CASE WHEN s.payment_status = 'PAID' AND s.last_payment_period = ? THEN 1 ELSE 0 END) as paid_students
                    FROM students s
                    JOIN student_subjects ss ON s.student_id = ss.student_id
                    JOIN subjects sub ON ss.subject_id = sub.subject_id
                    WHERE 1=1";
    
    $stats_params = [$period];
    $stats_types = 's';
    
    if (!empty($subject_filter)) {
        $stats_query .= " AND sub.subject_id = ?";
        $stats_params[] = $subject_filter;
        $stats_types .= 'i';
    }
    
    if (!empty($grade_filter)) {
        $stats_query .= " AND s.grade = ?";
        $stats_params[] = $grade_filter;
        $stats_types .= 's';
    }
    
    $stats_stmt = $conn->prepare($stats_query);
    $stats_stmt->bind_param($stats_types, ...$stats_params);
    $stats_stmt->execute();
    $stats_result = $stats_stmt->get_result();
    $stats = $stats_result->fetch_assoc();
    
    $total_students = $stats['total_students'];
    $paid_students = $stats['paid_students'];
    $unpaid_students = $total_students - $paid_students;
    $payment_rate = $total_students > 0 ? round(($paid_students / $total_students) * 100, 1) : 0;
    
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(60, 7, 'Total Students:', 0, 0);
    $pdf->Cell(0, 7, $total_students, 0, 1);
    
    $pdf->Cell(60, 7, 'Paid Students:', 0, 0);
    $pdf->Cell(0, 7, $paid_students, 0, 1);
    
    $pdf->Cell(60, 7, 'Unpaid Students:', 0, 0);
    $pdf->Cell(0, 7, $unpaid_students, 0, 1);
    
    $pdf->Cell(60, 7, 'Payment Rate:', 0, 0);
    $pdf->Cell(0, 7, $payment_rate . '%', 0, 1);
    
    // Output the PDF
    $pdf->Output('Payment_Report_' . $month_name . '_' . $year . '.pdf', 'D');
    
} catch (Exception $e) {
   
    echo "Error generating report: " . $e->getMessage();
    exit();
}
?>


